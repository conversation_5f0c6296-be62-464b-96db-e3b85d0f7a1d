@echo off
chcp 65001 >nul

setlocal enabledelayedexpansion

:: Check if running in PowerShell
set "powershell_check=%SystemRoot%\System32\WindowsPowerShell\v1.0\powershell.exe"
if exist "%powershell_check%" (
    for /f "delims=" %%i in ('powershell -command "[char]10024"') do set "START_EMOJI=%%i"
    for /f "delims=" %%i in ('powershell -command "[char]9989"') do set "CHECK_EMOJI=%%i"
    for /f "delims=" %%i in ('powershell -command "[char]10060"') do set "CROSS_EMOJI=%%i"
    for /f "delims=" %%i in ('powershell -command "[char]::ConvertFromUtf32(0x1F5D1)"') do set "DELETE_EMOJI=%%i"
    for /f "delims=" %%i in ('powershell -command "[char]::ConvertFromUtf32(0x1F4C2)"') do set "ZIP_EMOJI=%%i"
) else (
    set "START_EMOJI=✨"
    set "CHECK_EMOJI=✅"
    set "CROSS_EMOJI=❌"
    set "DELETE_EMOJI=🗑"
    set "ZIP_EMOJI=📂"
)

echo !START_EMOJI! Starting Blog Update

:: Ensure a ZIP file is passed as an argument
if "%~1"=="" (
    echo !CROSS_EMOJI! Error: No ZIP file provided.
    exit /b 1
)

set "ZIP_FILE=%~1"

:: Determine the script's directory and set the project root accordingly
set "SCRIPT_DIR=%~dp0"
set "PROJECT_ROOT=%SCRIPT_DIR%"
set "BLOG_DIR=%PROJECT_ROOT%public\blog"

:: Validate ZIP file
if /I not "%ZIP_FILE:~-4%"==".zip" (
    echo !CROSS_EMOJI! Error: The provided file is not a ZIP file.
    exit /b 1
)

:: Clear and extract new content
if exist "%BLOG_DIR%" (
    echo !DELETE_EMOJI! Clearing blog directory...
    rmdir /s /q "%BLOG_DIR%"
)
mkdir "%BLOG_DIR%"

tar -xf "%ZIP_FILE%" -C "%BLOG_DIR%" || (
    echo ⚠ CMD extraction failed. Trying PowerShell...
    powershell -command "if (Get-Command Expand-Archive -ErrorAction SilentlyContinue) { Expand-Archive -Path '%ZIP_FILE%' -DestinationPath '%BLOG_DIR%' -Force } else { exit 1 }" || (
        echo !CROSS_EMOJI! Error: Failed to extract ZIP file.
        exit /b 1
    )
)

echo !ZIP_EMOJI! ZIP extraction completed.

:: Check for Git changes
cd "%PROJECT_ROOT%"
git status --porcelain | findstr /r "." >nul && (
    for /f "delims=" %%b in ('git rev-parse --abbrev-ref HEAD') do set "CURRENT_BRANCH=%%b"
    git add .
    git commit -m "Updated blog content"
    git push origin !CURRENT_BRANCH! || (
        echo !CROSS_EMOJI! Error: Failed to push changes.
        exit /b 1
    )
    echo !CHECK_EMOJI! Blog update completed successfully.
) || (
    echo No changes detected. Skipping commit and push.
)

pause
