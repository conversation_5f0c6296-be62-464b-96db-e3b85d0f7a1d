#!/bin/bash

# Emoji definitions
START_EMOJI="✨"
CHECK_EMOJI="✅"
CROSS_EMOJI="❌"
DELETE_EMOJI="🗑"
ZIP_EMOJI="📂"

echo "$START_EMOJI Starting Blog Update"

# Check if running with proper arguments
if [ $# -eq 0 ]; then
    echo "$CROSS_EMOJI Error: No ZIP file provided."
    exit 1
fi

ZIP_FILE="$1"

# Determine script's directory and set project root
SCRIPT_DIR=$(dirname "$0")
PROJECT_ROOT="$SCRIPT_DIR"
BLOG_DIR="$PROJECT_ROOT/public/blog"

# Validate ZIP file
if [[ ! "$ZIP_FILE" =~ \.zip$ ]]; then
    echo "$CROSS_EMOJI Error: The provided file is not a ZIP file."
    exit 1
fi

# Clear and extract new content
echo "$DELETE_EMOJI Clearing blog directory..."
rm -rf "$BLOG_DIR"
mkdir -p "$BLOG_DIR"

# Extract ZIP file
echo "$ZIP_EMOJI Extracting ZIP file..."
if ! unzip "$ZIP_FILE" -d "$BLOG_DIR"; then
    echo "$CROSS_EMOJI Error: Failed to extract ZIP file."
    exit 1
fi

# Check for Git changes
cd "$PROJECT_ROOT" || exit 1
if [ -n "$(git status --porcelain)" ]; then
    CURRENT_BRANCH=$(git rev-parse --abbrev-ref HEAD)
    git add .
    git commit -m "Updated blog content from $(basename "$ZIP_FILE")"
    if ! git push origin "$CURRENT_BRANCH"; then
        echo "$CROSS_EMOJI Error: Failed to push changes."
        exit 1
    fi
    echo "$CHECK_EMOJI Blog update completed successfully."
else
    echo "No changes detected. Skipping commit and push."
fi

exit 0