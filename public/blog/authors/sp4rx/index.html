<!DOCTYPE html><html lang="en-gb"><head><meta charset="utf-8"><meta http-equiv="X-UA-Compatible" content="IE=edge"><meta name="viewport" content="width=device-width,initial-scale=1"><title>Author: Sp4Rx - Melodyze</title><meta name="robots" content="noindex, follow"><meta name="generator" content="Publii Open-Source CMS for Static Site"><link rel="alternate" type="application/atom+xml" href="https://melodyze.ai/blog/feed.xml"><link rel="alternate" type="application/json" href="https://melodyze.ai/blog/feed.json"><meta property="og:title" content="Sp4Rx"><meta property="og:image" content="https://melodyze.ai/blog/media/website/Melodyze-new-jacket-3.svg"><meta property="og:image:width" content="485"><meta property="og:image:height" content="426"><meta property="og:site_name" content="Melodyze"><meta property="og:description" content=""><meta property="og:url" content="https://melodyze.ai/blog/authors/sp4rx/"><meta property="og:type" content="website"><link rel="next" href="https://melodyze.ai/blog/authors/sp4rx/page/2/"><link rel="stylesheet" href="https://melodyze.ai/blog/assets/css/style.css?v=5a4975678b5a440fd4d322e87c4c644d"><script type="application/ld+json">{"@context":"http://schema.org","@type":"Organization","name":"Melodyze","logo":"https://melodyze.ai/blog/media/website/Melodyze-new-jacket-3.svg","url":"https://melodyze.ai/blog/","sameAs":[]}</script><noscript><style>img[loading] {
                    opacity: 1;
                }</style></noscript></head><body class="author-template"><header class="top js-header"><a class="logo" href="https://melodyze.ai/blog/"><img src="https://melodyze.ai/blog/media/website/Melodyze-new-jacket-3.svg" alt="Melodyze" width="485" height="426"></a><nav class="navbar js-navbar"><button class="navbar__toggle js-toggle" aria-label="Menu" aria-haspopup="true" aria-expanded="false"><span class="navbar__toggle-box"><span class="navbar__toggle-inner">Menu</span></span></button><ul class="navbar__menu"><li><a href="https://melodyze.ai/blog/https://melodyze.ai" target="_self">melodyze.ai</a></li><li><a href="https://melodyze.ai/blog/modi-budget-2/" target="_self">Modi budget</a></li></ul></nav></header><main class="page page--author"><div class="hero hero--noimage"><header class="hero__content"><div class="wrapper page--author__wrapper"><img src="https://www.gravatar.com/avatar/c4cd04d21360f8cb7a818518e494fd76?s&#x3D;240" loading="lazy" height="240" width="240" class="page--author__avatar" alt="Sp4Rx"><div><h1>Sp4Rx <sup>(7)</sup></h1><div class="page--author__website"><a href="https://suvajit.in" target="_blank" rel="nofollow noreferrer noopener" class="btn btn--icon"><span>Visit website</span> <svg height="18" width="18" aria-hidden="true"><use xlink:href="https://melodyze.ai/blog/assets/svg/svg-map.svg#website"/></svg></a></div></div></div></header></div><div class="wrapper feed"><article class="feed__item"><div class="feed__content"><header><div class="feed__meta"><img src="https://www.gravatar.com/avatar/c4cd04d21360f8cb7a818518e494fd76?s&#x3D;240" loading="lazy" height="240" width="240" class="feed__author-thumb" alt=""> <a href="https://melodyze.ai/blog/authors/sp4rx/" class="feed__author">Sp4Rx</a> <time datetime="2025-02-05T21:56" class="feed__date">February 5, 2025</time></div><h2 class="feed__title"><a href="https://melodyze.ai/blog/a-comprehensive-guide-to-testing-your-studio-setup-for-professional-sound/">A Comprehensive Guide to Testing Your Studio Setup for Professional Sound</a></h2></header><p>Before diving into a full recording session, it's essential to test your setup thoroughly. This ensures you capture high-quality audio, avoid unnecessary retakes, and save time in post-production. Testing your studio setup can seem like a small step, but it makes a huge difference in&hellip;</p><a href="https://melodyze.ai/blog/a-comprehensive-guide-to-testing-your-studio-setup-for-professional-sound/" class="readmore feed__readmore">Continue reading...</a></div></article><article class="feed__item"><figure class="feed__image"><img src="https://melodyze.ai/blog/media/posts/8/florian-olivo-4hbJ-eymZ1o-unsplash.jpg" srcset="https://melodyze.ai/blog/media/posts/8/responsive/florian-olivo-4hbJ-eymZ1o-unsplash-xs.webp 640w, https://melodyze.ai/blog/media/posts/8/responsive/florian-olivo-4hbJ-eymZ1o-unsplash-sm.webp 768w, https://melodyze.ai/blog/media/posts/8/responsive/florian-olivo-4hbJ-eymZ1o-unsplash-md.webp 1024w" sizes="(min-width: 600px) calc(4.38vw + 143px), 87.86vw" loading="lazy" height="4000" width="6000" alt=""></figure><div class="feed__content"><header><div class="feed__meta"><img src="https://www.gravatar.com/avatar/c4cd04d21360f8cb7a818518e494fd76?s&#x3D;240" loading="lazy" height="240" width="240" class="feed__author-thumb" alt=""> <a href="https://melodyze.ai/blog/authors/sp4rx/" class="feed__author">Sp4Rx</a> <time datetime="2025-02-04T06:55" class="feed__date">February 4, 2025</time></div><h2 class="feed__title"><a href="https://melodyze.ai/blog/emoji-in-powershell/">Emoji in Powershell</a></h2></header><div class="post__toc"><h3>Table of Contents</h3><ul><li><a href="#mcetoc_1ij7955u91i">Objectives:</a></li><li><a href="#mcetoc_1ij7955u91j">1. Object Model Design</a><ul><li><a href="#mcetoc_1ij7955u91k">Song Base Schema:</a></li></ul></li><li><a href="#mcetoc_1ij7955u91l">2. Node.js Server</a><ul><li><a href="#mcetoc_1ij7955u91m">Project Structure:</a></li><li><a href="#mcetoc_1ij7955u91n">app.js:</a></li></ul></li><li><a href="#mcetoc_1ij7955u91o">3. Song Routes (routes/songs.js)</a><ul><li><a href="#mcetoc_1ij7955u91p">File: routes/songs.js</a></li></ul></li><li><a href="#mcetoc_1ij7955u91q">4. Database Example</a></li><li><a href="#mcetoc_1ij7955u91r">5. Frontend Integration</a></li><li><a href="#mcetoc_1ij7955u91s">6. Improvements</a></li></ul></div><pre class="language-batch"><code>@echo off
chcp 65001 &gt;nul

echo [DEBUG] Codepage set to UTF-8

setlocal enabledelayedexpansion

echo [DEBUG] Delayed variable expansion enabled

:: Check if running in PowerShell
set "powershell_check=%SystemRoot%\System32\WindowsPowerShell\v1.0\powershell.exe"
echo [DEBUG] Checking for PowerShell at %powershell_check%

if exist "%powershell_check%" (
    echo [DEBUG] PowerShell found, fetching emojis
    for /f "delims=" %%i in ('powershell -command "[char]10024"') do set "START_EMOJI=%%i"
    for /f "delims=" %%i in ('powershell -command "[char]9989"') do set "CHECK_EMOJI=%%i"
) else (
    echo [DEBUG] PowerShell not found, using default emojis
    set "START_EMOJI=✨"
    set "CHECK_EMOJI=✅"
)

echo [DEBUG] START_EMOJI is set to !START_EMOJI!
echo [DEBUG] CHECK_EMOJI is set to !CHECK_EMOJI!

echo !START_EMOJI! Starting Deployment

echo [DEBUG] Running Firebase deployment command
REM Deploy only hosting for the test website
firebase deploy --project melodyze-65923 --only hosting:melodyze-ai

if %ERRORLEVEL% neq 0 (
    echo [ERROR] Deployment failed with exit code %ERRORLEVEL%
) else (
    echo !CHECK_EMOJI! Deployment Completed
)

echo [DEBUG] Script execution finished
pause
</code></pre><a href="https://melodyze.ai/blog/emoji-in-powershell/" class="readmore feed__readmore">Continue reading...</a></div></article><article class="feed__item"><figure class="feed__image"><img src="https://melodyze.ai/blog/media/posts/7/poly-lakeside-wallpaper-2.jpg" srcset="https://melodyze.ai/blog/media/posts/7/responsive/poly-lakeside-wallpaper-2-xs.webp 640w, https://melodyze.ai/blog/media/posts/7/responsive/poly-lakeside-wallpaper-2-sm.webp 768w, https://melodyze.ai/blog/media/posts/7/responsive/poly-lakeside-wallpaper-2-md.webp 1024w" sizes="(min-width: 600px) calc(4.38vw + 143px), 87.86vw" loading="lazy" height="2160" width="3840" alt=""></figure><div class="feed__content"><header><div class="feed__meta"><img src="https://www.gravatar.com/avatar/c4cd04d21360f8cb7a818518e494fd76?s&#x3D;240" loading="lazy" height="240" width="240" class="feed__author-thumb" alt=""> <a href="https://melodyze.ai/blog/authors/sp4rx/" class="feed__author">Sp4Rx</a> <time datetime="2025-02-04T06:41" class="feed__date">February 4, 2025</time></div><h2 class="feed__title"><a href="https://melodyze.ai/blog/feature-image/">Feature Image</a></h2></header><p>You can absolutely set a featured image for each post in Publii—just like Ghost—and you can even incorporate images from Unsplash if you wish. Here’s a breakdown of how to achieve that: Using the Built‐In Featured Image Functionality: When you create or edit a post&hellip;</p><a href="https://melodyze.ai/blog/feature-image/" class="readmore feed__readmore">Continue reading...</a></div></article><article class="feed__item"><div class="feed__content"><header><div class="feed__meta"><img src="https://www.gravatar.com/avatar/c4cd04d21360f8cb7a818518e494fd76?s&#x3D;240" loading="lazy" height="240" width="240" class="feed__author-thumb" alt=""> <a href="https://melodyze.ai/blog/authors/sp4rx/" class="feed__author">Sp4Rx</a> <time datetime="2025-02-03T03:31" class="feed__date">February 3, 2025</time></div><h2 class="feed__title"><a href="https://melodyze.ai/blog/modi-budget/">Modi budget</a></h2></header><p>The Prime Minister Shri Narendra Modi delivered his remarks during Maha Kumbabhishegam of Shri Sanathana Dharma Aalayam in Jakarta, Indonesia via video message today. He extended warm greetings to His Excellency, President Prabowo Subianto, Chairman of the Murugan Temple Trust Pa Hashim, Managing Trustee Dr.</p><a href="https://melodyze.ai/blog/modi-budget/" class="readmore feed__readmore">Continue reading...</a></div></article><article class="feed__item"><div class="feed__content"><header><div class="feed__meta"><img src="https://www.gravatar.com/avatar/c4cd04d21360f8cb7a818518e494fd76?s&#x3D;240" loading="lazy" height="240" width="240" class="feed__author-thumb" alt=""> <a href="https://melodyze.ai/blog/authors/sp4rx/" class="feed__author">Sp4Rx</a> <time datetime="2025-02-03T03:12" class="feed__date">February 3, 2025</time></div><h2 class="feed__title"><a href="https://melodyze.ai/blog/firebase/">Firebase</a></h2></header><p>Yes. Publii generates a fully static website—HTML, CSS, and JavaScript files—that you can host on any static hosting provider, including Firebase Hosting. In practice, you’d simply configure Firebase Hosting to serve the folder where Publii exports your site (often named “public” or another folder you&hellip;</p><a href="https://melodyze.ai/blog/firebase/" class="readmore feed__readmore">Continue reading...</a></div></article><nav class="wrapper pagination desc"><div class="pagination__item"><a href="https://melodyze.ai/blog/authors/sp4rx/page/2/" class="btn btn--icon"><svg width="20" height="20" aria-hidden="true"><use xlink:href="https://melodyze.ai/blog/assets/svg/svg-map.svg#arrow-prev"/></svg> <span>Previous</span></a></div></nav></div></main><footer class="footer"><div class="wrapper"><div class="footer__copyright"><p>© 2025 Melodyze.ai. All Rights Reserved. | Privacy Policy | Terms of Service | Contact Us<br>Follow us: Facebook | Twitter | Instagram | LinkedIn<br><br>Discover the future of music with Melodyze.ai</p></div><div class="footer__social"></div><button onclick="backToTopFunction()" id="backToTop" class="footer__bttop" aria-label="Back to top" title="Back to top"><svg width="20" height="20"><use xlink:href="https://melodyze.ai/blog/assets/svg/svg-map.svg#toparrow"/></svg></button></div></footer><script defer="defer" src="https://melodyze.ai/blog/assets/js/scripts.min.js?v=700105c316933a8202041b6415abb233"></script><script>window.publiiThemeMenuConfig={mobileMenuMode:'sidebar',animationSpeed:300,submenuWidth: 'auto',doubleClickTime:500,mobileMenuExpandableSubmenus:true,relatedContainerForOverlayMenuSelector:'.top'};</script><script>var images = document.querySelectorAll('img[loading]');
        for (var i = 0; i < images.length; i++) {
            if (images[i].complete) {
                images[i].classList.add('is-loaded');
            } else {
                images[i].addEventListener('load', function () {
                    this.classList.add('is-loaded');
                }, false);
            }
        }</script><div class="pcb" data-behaviour="badge" data-behaviour-link="#cookie-settings" data-revision="1" data-config-ttl="90" data-debug-mode="false"><div role="dialog" aria-modal="true" aria-hidden="true" aria-labelledby="pcb-title" aria-describedby="pcb-txt" class="pcb__banner"><div class="pcb__inner"><div id="pcb-title" role="heading" aria-level="2" class="pcb__title">This website uses cookies</div><div id="pcb-txt" class="pcb__txt">Select which cookies to opt-in to via the checkboxes below; our website uses cookies to examine site traffic and user activity while on our site, for marketing, and to provide social media functionality. <a href="https://melodyze.ai/privacy-policy">More details...</a></div><div class="pcb__buttons"><button type="button" class="pcb__btn pcb__btn--link pcb__btn--configure" aria-haspopup="dialog">Manage preferences</button> <button type="button" class="pcb__btn pcb__btn--solid pcb__btn--accept">Accept all</button></div></div></div><div class="pcb__popup" role="dialog" aria-modal="true" aria-hidden="true" aria-labelledby="pcb-popup-title"><div class="pcb__popup__wrapper"><div class="pcb__inner pcb__popup__inner"><div class="pcb__popup__heading"><div id="pcb-popup-title" role="heading" aria-level="2" class="pcb__title">Cookie settings</div><button class="pcb__popup__close" aria-label="Close"></button></div><div class="pcb__popup__content"><div class="pcb__txt pcb__popup__txt">We use cookies to enhance your browsing experience, serve personalized ads or content, and analyze our traffic. By clicking "Accept All", you consent to our use of cookies. <a href="https://melodyze.ai/privacy-policy">More details...</a></div><ul class="pcb__groups"><li class="pcb__group"><details><summary class="pcb__group__title no-desc">Required</summary></details><div class="pcb__popup__switch is-checked"><input type="checkbox" data-group-name="" id="pcb-group-0" checked="checked"> <label for="pcb-group-0">Required</label></div></li></ul></div><div class="pcb__buttons pcb__popup__buttons"><button type="button" class="pcb__btn pcb__btn--solid pcb__btn--accept">Accept all</button> <button type="button" class="pcb__btn pcb__btn--reject">Reject all</button> <button type="button" class="pcb__btn pcb__btn--save">Save settings</button></div></div></div></div><div class="pcb__overlay" aria-hidden="true"></div><button class="pcb__badge" aria-label="Cookie Policy" aria-hidden="true"><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" width="40" height="40" viewBox="0 0 23 23" fill="currentColor"><path d="M21.41 12.71c-.08-.01-.15 0-.22 0h-.03c-.03 0-.05 0-.08.01-.07 0-.13.01-.19.04-.52.21-1.44.19-2.02-.22-.44-.31-.65-.83-.62-1.53a.758.758 0 0 0-.27-.61.73.73 0 0 0-.65-.14c-1.98.51-3.49.23-4.26-.78-.82-1.08-.73-2.89.24-4.49.14-.23.14-.52 0-.75a.756.756 0 0 0-.67-.36c-.64.03-1.11-.1-1.31-.35-.19-.26-.13-.71-.01-1.29.04-.18.06-.38.03-.59-.05-.4-.4-.7-.81-.66C5.1 1.54 1 6.04 1 11.48 1 17.28 5.75 22 11.6 22c5.02 0 9.39-3.54 10.39-8.42.08-.4-.18-.78-.58-.87Zm-9.81 7.82c-5.03 0-9.12-4.06-9.12-9.06 0-4.34 3.05-8 7.25-8.86-.08.7.05 1.33.42 1.81.24.32.66.67 1.38.84-.76 1.86-.65 3.78.36 5.11.61.81 2.03 2 4.95 1.51.18.96.71 1.54 1.18 1.87.62.43 1.38.62 2.1.62.05 0 .09 0 .13-.01-1.23 3.64-4.7 6.18-8.64 6.18ZM13 17c0 .55-.45 1-1 1s-1-.45-1-1 .45-1 1-1 1 .45 1 1Zm5.29-12.3a.99.99 0 0 1-.29-.71c0-.55.45-.99 1-.99a1 1 0 0 1 .71.3c.19.19.29.44.29.71 0 .55-.45.99-1 .99a1 1 0 0 1-.71-.3ZM9 13.5c0 .83-.67 1.5-1.5 1.5S6 14.33 6 13.5 6.67 12 7.5 12s1.5.67 1.5 1.5Zm3.25.81a.744.744 0 0 1-.06-1.05c.28-.32.75-.34 1.05-.***********.75.05 1.06-.15.16-.35.25-.56.25-.18 0-.36-.06-.5-.19ZM8.68 7.26c.41.37.44 1 .07 1.41-.2.22-.47.33-.75.33a.96.96 0 0 1-.67-.26c-.41-.37-.44-1-.07-1.41.37-.42 1-.45 1.41-.08Zm11.48 1.88c.18-.19.52-.19.7 0 .**********.***********.*********** 0 .13-.05.26-.15.35-.09.1-.22.15-.35.15s-.26-.05-.35-.15a.355.355 0 0 1-.11-.16.433.433 0 0 1-.04-.19c0-.13.05-.26.15-.35Zm-4.93-1.86a.75.75 0 1 1 1.059-********** 0 0 1-1.059 1.06Z"/></svg></button></div><script>(function(win) {
    if (!document.querySelector('.pcb')) {
        return;
    }

    var cbConfig = {
        behaviour: document.querySelector('.pcb').getAttribute('data-behaviour'),
        behaviourLink: document.querySelector('.pcb').getAttribute('data-behaviour-link'),
        revision: document.querySelector('.pcb').getAttribute('data-revision'),
        configTTL: parseInt(document.querySelector('.pcb').getAttribute('data-config-ttl'), 10),
        debugMode: document.querySelector('.pcb').getAttribute('data-debug-mode') === 'true',
        initialState: null,
        initialLsState: null,
        previouslyAccepted: []
    };

    var cbUI = {
        wrapper: document.querySelector('.pcb'),
        banner: {
            element: null,
            btnAccept: null,
            btnReject: null,
            btnConfigure: null
        },
        popup: {
            element: null,
            btnClose: null,
            btnSave: null,
            btnAccept: null,
            btnReject: null,
            checkboxes: null,
        },
        overlay: null,
        badge: null,
        blockedScripts: document.querySelectorAll('script[type^="gdpr-blocker/"]'),
        triggerLinks: cbConfig.behaviourLink ? document.querySelectorAll('a[href*="' + cbConfig.behaviourLink + '"]') : null
    };

    function initUI () {
        // setup banner elements
        cbUI.banner.element = cbUI.wrapper.querySelector('.pcb__banner');
        cbUI.banner.btnAccept = cbUI.banner.element.querySelector('.pcb__btn--accept');
        cbUI.banner.btnReject = cbUI.banner.element.querySelector('.pcb__btn--reject');
        cbUI.banner.btnConfigure = cbUI.banner.element.querySelector('.pcb__btn--configure');

        // setup popup elements
        if (cbUI.wrapper.querySelector('.pcb__popup')) {
            cbUI.popup.element = cbUI.wrapper.querySelector('.pcb__popup');
            cbUI.popup.btnClose = cbUI.wrapper.querySelector('.pcb__popup__close');
            cbUI.popup.btnSave = cbUI.popup.element.querySelector('.pcb__btn--save');
            cbUI.popup.btnAccept = cbUI.popup.element.querySelector('.pcb__btn--accept');
            cbUI.popup.btnReject = cbUI.popup.element.querySelector('.pcb__btn--reject');
            cbUI.popup.checkboxes = cbUI.popup.element.querySelector('input[type="checkbox"]');
            // setup overlay
            cbUI.overlay = cbUI.wrapper.querySelector('.pcb__overlay');
        }

        cbUI.badge = cbUI.wrapper.querySelector('.pcb__badge');

        if (cbConfig.behaviour.indexOf('link') > -1) {
            for (var i = 0; i < cbUI.triggerLinks.length; i++) {
                cbUI.triggerLinks[i].addEventListener('click', function(e) {
                    e.preventDefault();
                    showBannerOrPopup();
                });
            }
        }
    }

    function initState () {
        var lsKeyName = getConfigName();
        var currentConfig = localStorage.getItem(lsKeyName);
        var configIsFresh = checkIfConfigIsFresh();

        if (!configIsFresh || currentConfig === null) {
            if (cbConfig.debugMode) {
                console.log('🍪 Config not found, or configuration expired');
            }

            if (window.publiiCBGCM) {
                gtag('consent', 'default', {
                    'ad_storage': window.publiiCBGCM.defaultState.ad_storage ? 'granted' : 'denied',
                    'ad_personalization': window.publiiCBGCM.defaultState.ad_personalization ? 'granted' : 'denied',
                    'ad_user_data': window.publiiCBGCM.defaultState.ad_user_data ? 'granted' : 'denied',
                    'analytics_storage': window.publiiCBGCM.defaultState.analytics_storage ? 'granted' : 'denied',
                    'personalization_storage': window.publiiCBGCM.defaultState.personalization_storage ? 'granted' : 'denied',
                    'functionality_storage': window.publiiCBGCM.defaultState.functionality_storage ? 'granted' : 'denied',
                    'security_storage': window.publiiCBGCM.defaultState.security_storage ? 'granted' : 'denied'
                });  
                
                if (cbConfig.debugMode) {
                    console.log('🍪 GCMv2 DEFAULT STATE: ' + JSON.stringify({
                        'ad_storage': window.publiiCBGCM.defaultState.ad_storage ? 'granted' : 'denied',
                        'ad_personalization': window.publiiCBGCM.defaultState.ad_personalization ? 'granted' : 'denied',
                        'ad_user_data': window.publiiCBGCM.defaultState.ad_user_data ? 'granted' : 'denied',
                        'analytics_storage': window.publiiCBGCM.defaultState.analytics_storage ? 'granted' : 'denied',
                        'personalization_storage': window.publiiCBGCM.defaultState.personalization_storage ? 'granted' : 'denied',
                        'functionality_storage': window.publiiCBGCM.defaultState.functionality_storage ? 'granted' : 'denied',
                        'security_storage': window.publiiCBGCM.defaultState.security_storage ? 'granted' : 'denied'
                    }));
                }
            }

            showBanner();
        } else if (typeof currentConfig === 'string') {
            if (cbConfig.debugMode) {
                console.log('🍪 Config founded');
            }

            cbConfig.initialLsState = currentConfig.split(',');

            if (window.publiiCBGCM) {
                gtag('consent', 'default', {
                    'ad_storage': getDefaultConsentState(currentConfig, 'ad_storage'),
                    'ad_personalization': getDefaultConsentState(currentConfig, 'ad_personalization'),
                    'ad_user_data': getDefaultConsentState(currentConfig, 'ad_user_data'),
                    'analytics_storage': getDefaultConsentState(currentConfig, 'analytics_storage'),
                    'personalization_storage': getDefaultConsentState(currentConfig, 'personalization_storage'),
                    'functionality_storage': getDefaultConsentState(currentConfig, 'functionality_storage'),
                    'security_storage': getDefaultConsentState(currentConfig, 'security_storage')
                });
                
                if (cbConfig.debugMode) {
                    console.log('🍪 GCMv2 DEFAULT STATE: ' + JSON.stringify({
                        'ad_storage': getDefaultConsentState(currentConfig, 'ad_storage'),
                        'ad_personalization': getDefaultConsentState(currentConfig, 'ad_personalization'),
                        'ad_user_data': getDefaultConsentState(currentConfig, 'ad_user_data'),
                        'analytics_storage': getDefaultConsentState(currentConfig, 'analytics_storage'),
                        'personalization_storage': getDefaultConsentState(currentConfig, 'personalization_storage'),
                        'functionality_storage': getDefaultConsentState(currentConfig, 'functionality_storage'),
                        'security_storage': getDefaultConsentState(currentConfig, 'security_storage')
                    }));
                }
            }

            showBadge();

            if (cbUI.popup.element) {
                var allowedGroups = currentConfig.split(',');
                var checkedCheckboxes = cbUI.popup.element.querySelectorAll('input[type="checkbox"]:checked');

                for (var j = 0; j < checkedCheckboxes.length; j++) {
                    var name = checkedCheckboxes[j].getAttribute('data-group-name');

                    if (name && name !== '-' && allowedGroups.indexOf(name) === -1) {
                        checkedCheckboxes[j].checked = false;
                    }
                }

                for (var i = 0; i < allowedGroups.length; i++) {
                    var checkbox = cbUI.popup.element.querySelector('input[type="checkbox"][data-group-name="' + allowedGroups[i] + '"]');

                    if (checkbox) {
                        checkbox.checked = true;
                    }

                    allowCookieGroup(allowedGroups[i]);
                }
            }
        }

        setTimeout(function () {
            cbConfig.initialState = getInitialStateOfConsents();
        }, 0);
    }

    function checkIfConfigIsFresh () {
        var lastConfigSave = localStorage.getItem('publii-gdpr-cookies-config-save-date');

        if (lastConfigSave === null) {
            return false;
        }

        lastConfigSave = parseInt(lastConfigSave, 10);

        if (lastConfigSave === 0) {
            return true;
        }

        if (+new Date() - lastConfigSave < cbConfig.configTTL * 24 * 60 * 60 * 1000) {
            return true;
        }

        return false;
    }

    function getDefaultConsentState (currentConfig, consentGroup) {
        let configGroups = currentConfig.split(',');

        for (let i = 0; i < configGroups.length; i++) {
            let groupName = configGroups[i];
            let group = window.publiiCBGCM.groups.find(group => group.cookieGroup === groupName);

            if (group && group[consentGroup]) {
                return 'granted';
            }
        }  
        
        if (window.publiiCBGCM.defaultState[consentGroup]) {
            return 'granted'; 
        }
        
        return 'denied';
    }

    function initBannerEvents () {
        cbUI.banner.btnAccept.addEventListener('click', function (e) {
            e.preventDefault();
            acceptAllCookies('banner');
            showBadge();
        }, false);

        if (cbUI.banner.btnReject) {
            cbUI.banner.btnReject.addEventListener('click', function (e) {
                e.preventDefault();
                rejectAllCookies();
                showBadge();
            }, false);
        }

        if (cbUI.banner.btnConfigure) {
            cbUI.banner.btnConfigure.addEventListener('click', function (e) {
                e.preventDefault();
                hideBanner();
                showAdvancedPopup();
                showBadge();
            }, false);
        }
    }

    function initPopupEvents () {
        if (!cbUI.popup.element) {
            return;
        }

        cbUI.overlay.addEventListener('click', function (e) {
            hideAdvancedPopup();
        }, false);

        cbUI.popup.element.addEventListener('click', function (e) {
            e.stopPropagation();
        }, false);

        cbUI.popup.btnAccept.addEventListener('click', function (e) {
            e.preventDefault();
            acceptAllCookies('popup');
        }, false);

        cbUI.popup.btnReject.addEventListener('click', function (e) {
            e.preventDefault();
            rejectAllCookies();
        }, false);

        cbUI.popup.btnSave.addEventListener('click', function (e) {
            e.preventDefault();
            saveConfiguration();
        }, false);

        cbUI.popup.btnClose.addEventListener('click', function (e) {
            e.preventDefault();
            hideAdvancedPopup();
        }, false);
    }

    function initBadgeEvents () {
        if (!cbUI.badge) {
            return;
        }

        cbUI.badge.addEventListener('click', function (e) {
            showBannerOrPopup();
        }, false);
    }

    initUI();
    initState();
    initBannerEvents();
    initPopupEvents();
    initBadgeEvents();

    /**
     * API
     */
    function addScript (src, inline) {
        var newScript = document.createElement('script');

        if (src) {
            newScript.setAttribute('src', src);
        }

        if (inline) {
            newScript.text = inline;
        }

        document.body.appendChild(newScript);
    }

    function allowCookieGroup (allowedGroup) {
        var scripts = document.querySelectorAll('script[type="gdpr-blocker/' + allowedGroup + '"]');
        cbConfig.previouslyAccepted.push(allowedGroup);
    
        for (var j = 0; j < scripts.length; j++) {
            addScript(scripts[j].src, scripts[j].text);
        }

        var groupEvent = new Event('publii-cookie-banner-unblock-' + allowedGroup);
        document.body.dispatchEvent(groupEvent);
        unlockEmbeds(allowedGroup);

        if (cbConfig.debugMode) {
            console.log('🍪 Allowed group: ' + allowedGroup);
        }

        if (window.publiiCBGCM && cbConfig.initialLsState.indexOf(allowedGroup) === -1) {
            let consentResult = {};
            let group = window.publiiCBGCM.groups.find(group => group.cookieGroup === allowedGroup);

            if (group) {
                let foundSomeConsents = false;

                Object.keys(group).forEach(key => {
                    if (key !== 'cookieGroup' && group[key] === true) {
                        consentResult[key] = 'granted';
                        foundSomeConsents = true;
                    }
                });

                if (foundSomeConsents) {
                    gtag('consent', 'update', consentResult);   

                    if (cbConfig.debugMode) {
                        console.log('🍪 GCMv2 UPDATE: ' + JSON.stringify(consentResult));
                    }
                }
            }
        }
    }

    function showBannerOrPopup () {
        if (cbUI.popup.element) {
            showAdvancedPopup();
        } else {
            showBanner();
        }
    }

    function showAdvancedPopup () {
        cbUI.popup.element.classList.add('is-visible');
        cbUI.overlay.classList.add('is-visible');
        cbUI.popup.element.setAttribute('aria-hidden', 'false');
        cbUI.overlay.setAttribute('aria-hidden', 'false');
    }

    function hideAdvancedPopup () {
        cbUI.popup.element.classList.remove('is-visible');
        cbUI.overlay.classList.remove('is-visible');
        cbUI.popup.element.setAttribute('aria-hidden', 'true');
        cbUI.overlay.setAttribute('aria-hidden', 'true');
    }

    function showBanner () {
        cbUI.banner.element.classList.add('is-visible');
        cbUI.banner.element.setAttribute('aria-hidden', 'false');
    }

    function hideBanner () {
        cbUI.banner.element.classList.remove('is-visible');
        cbUI.banner.element.setAttribute('aria-hidden', 'true');
    }

    function showBadge () {
        if (!cbUI.badge) {
            return;
        }

        cbUI.badge.classList.add('is-visible');
        cbUI.badge.setAttribute('aria-hidden', 'false');
    }

    function getConfigName () {
        var lsKeyName = 'publii-gdpr-allowed-cookies';

        if (cbConfig.revision) {
            lsKeyName = lsKeyName + '-v' + parseInt(cbConfig.revision, 10);
        }

        return lsKeyName;
    }

    function storeConfiguration (allowedGroups) {
        var lsKeyName = getConfigName();
        var dataToStore = allowedGroups.join(',');
        localStorage.setItem(lsKeyName, dataToStore);

        if (cbConfig.configTTL === 0) {
            localStorage.setItem('publii-gdpr-cookies-config-save-date', 0);

            if (cbConfig.debugMode) {
                console.log('🍪 Store never expiring configuration');
            }
        } else {
            localStorage.setItem('publii-gdpr-cookies-config-save-date', +new Date());
        }
    }

    function getInitialStateOfConsents () {
        if (!cbUI.popup.element) {
            return [];
        }

        var checkedGroups = cbUI.popup.element.querySelectorAll('input[type="checkbox"]:checked');
        var groups = [];

        for (var i = 0; i < checkedGroups.length; i++) {
            var allowedGroup = checkedGroups[i].getAttribute('data-group-name');

            if (allowedGroup !== '') {
                groups.push(allowedGroup);
            }
        }

        if (cbConfig.debugMode) {
            console.log('🍪 Initial state: ' + groups.join(', '));
        }

        return groups;
    }

    function getCurrentStateOfConsents () {
        if (!cbUI.popup.element) {
            return [];
        }

        var checkedGroups = cbUI.popup.element.querySelectorAll('input[type="checkbox"]:checked');
        var groups = [];

        for (var i = 0; i < checkedGroups.length; i++) {
            var allowedGroup = checkedGroups[i].getAttribute('data-group-name');

            if (allowedGroup !== '') {
                groups.push(allowedGroup);
            }
        }

        if (cbConfig.debugMode) {
            console.log('🍪 State to save: ' + groups.join(', '));
        }

        return groups;
    }

    function getAllGroups () {
        if (!cbUI.popup.element) {
            return [];
        }

        var checkedGroups = cbUI.popup.element.querySelectorAll('input[type="checkbox"]');
        var groups = [];

        for (var i = 0; i < checkedGroups.length; i++) {
            var allowedGroup = checkedGroups[i].getAttribute('data-group-name');

            if (allowedGroup !== '') {
                groups.push(allowedGroup);
            }
        }

        return groups;
    }

    function acceptAllCookies (source) {
        var groupsToAccept = getAllGroups();
        storeConfiguration(groupsToAccept);

        for (var i = 0; i < groupsToAccept.length; i++) {
            var group = groupsToAccept[i];

            if (cbConfig.initialState.indexOf(group) > -1 || cbConfig.previouslyAccepted.indexOf(group) > -1) {
                if (cbConfig.debugMode) {
                    console.log('🍪 Skip previously activated group: ' + group);
                }

                continue;
            }

            allowCookieGroup(group);
        }

        if (cbUI.popup.element) {
            var checkboxesToCheck = cbUI.popup.element.querySelectorAll('input[type="checkbox"]');

            for (var j = 0; j < checkboxesToCheck.length; j++) {
                checkboxesToCheck[j].checked = true;
            }
        }

        if (cbConfig.debugMode) {
            console.log('🍪 Accept all cookies: ', groupsToAccept.join(', '));
        }

        if (source === 'popup') {
            hideAdvancedPopup();
        } else if (source === 'banner') {
            hideBanner();
        }
    }

    function rejectAllCookies () {
        if (cbConfig.debugMode) {
            console.log('🍪 Reject all cookies');
        }

        storeConfiguration([]);
        setTimeout(function () {
            window.location.reload();
        }, 100);
    }

    function saveConfiguration () {
        var groupsToAccept = getCurrentStateOfConsents();
        storeConfiguration(groupsToAccept);

        if (cbConfig.debugMode) {
            console.log('🍪 Save new config: ', groupsToAccept.join(', '));
        }

        if (reloadIsNeeded(groupsToAccept)) {
            setTimeout(function () {
                window.location.reload();
            }, 100);
            return;
        }

        for (var i = 0; i < groupsToAccept.length; i++) {
            var group = groupsToAccept[i];

            if (cbConfig.initialState.indexOf(group) > -1 || cbConfig.previouslyAccepted.indexOf(group) > -1) {
                if (cbConfig.debugMode) {
                    console.log('🍪 Skip previously activated group: ' + group);
                }

                continue;
            }

            allowCookieGroup(group);
        }

        hideAdvancedPopup();
    }

    function reloadIsNeeded (groupsToAccept) {
        // check if user rejected consent for initial groups
        var initialGroups = cbConfig.initialState;
        var previouslyAcceptedGroups = cbConfig.previouslyAccepted;
        var groupsToCheck = initialGroups.concat(previouslyAcceptedGroups);

        for (var i = 0; i < groupsToCheck.length; i++) {
            var groupToCheck = groupsToCheck[i];

            if (groupToCheck !== '' && groupsToAccept.indexOf(groupToCheck) === -1) {
                if (cbConfig.debugMode) {
                    console.log('🍪 Reload is needed due lack of: ', groupToCheck);
                }

                return true;
            }
        }

        return false;
    }

    function unlockEmbeds (cookieGroup) {
        var iframesToUnlock = document.querySelectorAll('.pec-wrapper[data-consent-group-id="' + cookieGroup + '"]');

        for (var i = 0; i < iframesToUnlock.length; i++) {
            var iframeWrapper = iframesToUnlock[i];
            iframeWrapper.querySelector('.pec-overlay').classList.remove('is-active');
            iframeWrapper.querySelector('.pec-overlay').setAttribute('aria-hidden', 'true');
            var iframe = iframeWrapper.querySelector('iframe');
            iframe.setAttribute('src', iframe.getAttribute('data-consent-src'));
        }
    }

    win.publiiEmbedConsentGiven = function (cookieGroup) {
        // it will unlock embeds
        allowCookieGroup(cookieGroup);

        var checkbox = cbUI.popup.element.querySelector('input[type="checkbox"][data-group-name="' + cookieGroup + '"]');

        if (checkbox) {
            checkbox.checked = true;
        }

        var groupsToAccept = getCurrentStateOfConsents();
        storeConfiguration(groupsToAccept);

        if (cbConfig.debugMode) {
            console.log('🍪 Save new config: ', groupsToAccept.join(', '));
        }
    }
})(window);</script></body></html>