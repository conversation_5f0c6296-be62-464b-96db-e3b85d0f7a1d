<!DOCTYPE html><html lang="en-gb"><head><meta charset="utf-8"><meta http-equiv="X-UA-Compatible" content="IE=edge"><meta name="viewport" content="width=device-width,initial-scale=1"><title>Ghost blog setup - Melodyze</title><meta name="description" content="To integrate your main website hosted on Firebase with your Ghost blog hosted on AWS under the /blog subdirectory, you can utilize Cloudflare Workers to manage the routing seamlessly. Here's a concise guide to setting up this configuration: Provision an AWS EC2 Instance: Install Node.js and Ghost:&hellip;"><meta name="generator" content="Publii Open-Source CMS for Static Site"><link rel="canonical" href="https://melodyze.ai/blog/another-post/"><link rel="alternate" type="application/atom+xml" href="https://melodyze.ai/blog/feed.xml"><link rel="alternate" type="application/json" href="https://melodyze.ai/blog/feed.json"><meta property="og:title" content="Ghost blog setup"><meta property="og:image" content="https://melodyze.ai/blog/media/website/Melodyze-new-jacket-3.svg"><meta property="og:image:width" content="485"><meta property="og:image:height" content="426"><meta property="og:site_name" content="Melodyze"><meta property="og:description" content="To integrate your main website hosted on Firebase with your Ghost blog hosted on AWS under the /blog subdirectory, you can utilize Cloudflare Workers to manage the routing seamlessly. Here's a concise guide to setting up this configuration: Provision an AWS EC2 Instance: Install Node.js and Ghost:&hellip;"><meta property="og:url" content="https://melodyze.ai/blog//another-post/"><meta property="og:type" content="article"><link rel="stylesheet" href="https://melodyze.ai/blog/assets/css/style.css?v=5a4975678b5a440fd4d322e87c4c644d"><script type="application/ld+json">{"@context":"http://schema.org","@type":"Article","mainEntityOfPage":{"@type":"WebPage","@id":"https://melodyze.ai/blog/another-post/"},"headline":"Ghost blog setup","datePublished":"2025-02-03T03:09+05:30","dateModified":"2025-02-03T03:19+05:30","image":{"@type":"ImageObject","url":"https://melodyze.ai/blog/media/website/Melodyze-new-jacket-3.svg","height":426,"width":485},"description":"To integrate your main website hosted on Firebase with your Ghost blog hosted on AWS under the /blog subdirectory, you can utilize Cloudflare Workers to manage the routing seamlessly. Here's a concise guide to setting up this configuration: Provision an AWS EC2 Instance: Install Node.js and Ghost:&hellip;","author":{"@type":"Person","name":"Sp4Rx","url":"https://melodyze.ai/blog/authors/sp4rx/"},"publisher":{"@type":"Organization","name":"Sp4Rx","logo":{"@type":"ImageObject","url":"https://melodyze.ai/blog/media/website/Melodyze-new-jacket-3.svg","height":426,"width":485}}}</script><noscript><style>img[loading] {
                    opacity: 1;
                }</style></noscript></head><body class="post-template"><header class="top js-header"><a class="logo" href="https://melodyze.ai/blog/"><img src="https://melodyze.ai/blog/media/website/Melodyze-new-jacket-3.svg" alt="Melodyze" width="485" height="426"></a><nav class="navbar js-navbar"><button class="navbar__toggle js-toggle" aria-label="Menu" aria-haspopup="true" aria-expanded="false"><span class="navbar__toggle-box"><span class="navbar__toggle-inner">Menu</span></span></button><ul class="navbar__menu"><li><a href="https://melodyze.ai/blog/https://melodyze.ai" target="_self">melodyze.ai</a></li><li><a href="https://melodyze.ai/blog/modi-budget-2/" target="_self">Modi budget</a></li></ul></nav></header><main class="post"><article class="content"><div class="hero hero--noimage"><header class="hero__content"><div class="wrapper"><h1>Ghost blog setup</h1><div class="feed__meta content__meta"><img src="https://www.gravatar.com/avatar/c4cd04d21360f8cb7a818518e494fd76?s&#x3D;240" loading="eager" height="240" width="240" class="feed__author-thumb" alt=""> <a href="https://melodyze.ai/blog/authors/sp4rx/" class="feed__author">Sp4Rx</a> <time datetime="2025-02-03T03:09" class="feed__date">February 3, 2025</time></div></div></header></div><div class="entry-wrapper content__entry"><p>To integrate your main website hosted on Firebase with your Ghost blog hosted on AWS under the <strong>/blog</strong> subdirectory, you can utilize Cloudflare Workers to manage the routing seamlessly. Here's a concise guide to setting up this configuration:</p><h3>1. <strong>Set Up Your Ghost Blog on AWS</strong></h3><ul><li><p><strong>Provision an AWS EC2 Instance:</strong></p><ul><li>Launch an EC2 instance with Ubuntu 20.04 LTS or a compatible version.</li><li>Ensure the instance has the necessary security group settings to allow HTTP/HTTPS traffic.</li></ul></li><li><p><strong>Install Node.js and Ghost:</strong></p><ul><li>SSH into your EC2 instance.</li><li>Install Node.js (v16.x is recommended for Ghost v5.x).</li><li>Install the Ghost CLI globally:<pre><code class="language-bash">sudo npm install -g ghost-cli
</code></pre></li><li>Create a directory for your Ghost installation and set the appropriate permissions:<pre><code class="language-bash">sudo mkdir -p /var/www/ghost
sudo chown -R $USER:$USER /var/www/ghost
</code></pre></li><li>Navigate to the directory and install Ghost:<pre><code class="language-bash">cd /var/www/ghost
ghost install
</code></pre></li><li>During installation, when prompted for your blog URL, enter <code>https://www.melodyze.ai/blog</code>.</li></ul></li><li><p><strong>Configure Nginx as a Reverse Proxy:</strong></p><ul><li>Ghost installs and configures Nginx by default. Ensure that Nginx is set to proxy requests to your Ghost instance running on port 2368.</li></ul></li><li><p><strong>Secure with SSL:</strong></p><ul><li>Use Let's Encrypt to obtain an SSL certificate for your domain. Ghost's CLI can automate this:<pre><code class="language-bash">ghost setup ssl
</code></pre></li></ul></li></ul><h3>2. <strong>Configure Cloudflare</strong></h3><ul><li><p><strong>Add Your Domain to Cloudflare:</strong></p><ul><li>Sign up for a Cloudflare account and add <code>melodyze.ai</code> as a new site.</li><li>Update your domain's nameservers to point to Cloudflare's nameservers as provided during the setup.</li></ul></li><li><p><strong>Set Up DNS Records:</strong></p><ul><li>Ensure you have an A record for <code>www.melodyze.ai</code> pointing to your Firebase Hosting IP address.</li><li>If your Firebase Hosting uses a CNAME, configure it accordingly.</li></ul></li></ul><h3>3. <strong>Implement Cloudflare Workers for Path-Based Routing</strong></h3><ul><li><p><strong>Create a Cloudflare Worker:</strong></p><ul><li>In the Cloudflare dashboard, navigate to the "Workers" section and create a new worker.</li></ul></li><li><p><strong>Worker Script:</strong></p><ul><li>Use the following script to route traffic:<pre><code class="language-javascript">addEventListener('fetch', event =&gt; {
  const url = new URL(event.request.url);
  if (url.pathname.startsWith('/blog')) {
    // Remove '/blog' from the path and proxy to Ghost
    url.hostname = 'your-ec2-public-ip'; // Replace with your EC2 instance's public IP
    url.port = '2368'; // Default Ghost port
    url.pathname = url.pathname.replace('/blog', '');
    const request = new Request(url, event.request);
    event.respondWith(fetch(request));
  } else {
    // Serve from Firebase Hosting
    event.respondWith(fetch(event.request));
  }
});
</code></pre></li><li>Replace <code>'your-ec2-public-ip'</code> with the public IP address of your EC2 instance.</li></ul></li><li><p><strong>Deploy the Worker:</strong></p><ul><li>Save and deploy the worker script.</li></ul></li><li><p><strong>Assign the Worker to Your Domain:</strong></p><ul><li>In the Cloudflare dashboard, go to "Workers" and then "Triggers."</li><li>Set a route for <code>www.melodyze.ai/*</code> to use the newly created worker.</li></ul></li></ul><h3>4. <strong>Finalize Firebase Hosting Configuration</strong></h3><ul><li><strong>Redirect Non-WWW to WWW:</strong><ul><li>In your Firebase Hosting configuration, set up redirects to ensure all traffic to <code>melodyze.ai</code> is redirected to <code>www.melodyze.ai</code> for consistency.</li></ul></li></ul><h3>Summary</h3><p>By following this setup:</p><ul><li>Requests to <code>https://www.melodyze.ai</code> and other non-blog paths are served by your Firebase Hosting.</li><li>Requests to <code>https://www.melodyze.ai/blog</code> and its subpaths are proxied to your Ghost blog hosted on AWS.</li></ul><p>This configuration ensures a seamless user experience under a unified domain structure, which is beneficial for SEO and user navigation.</p><p><em>Note:</em> Ensure that your AWS instance's security group allows incoming traffic on the necessary ports and that your instance's firewall settings permit this traffic. Additionally, monitor your Cloudflare Worker for any potential issues or required updates.</p></div><footer class="content__footer"><div class="entry-wrapper"><p class="content__updated">This article was updated on February 3, 2025</p><div class="content__actions"><div class="content__share"><button class="btn--icon content__share-button js-content__share-button"><svg width="20" height="20" aria-hidden="true"><use xlink:href="https://melodyze.ai/blog/assets/svg/svg-map.svg#share"></use></svg> <span>Share It</span></button><div class="content__share-popup js-content__share-popup"><a href="https://www.facebook.com/sharer/sharer.php?u=https%3A%2F%2Fmelodyze.ai%2Fblog%2Fanother-post%2F" class="js-share facebook" rel="nofollow noopener noreferrer"><svg class="icon" aria-hidden="true" focusable="false"><use xlink:href="https://melodyze.ai/blog/assets/svg/svg-map.svg#facebook"/></svg> <span>Facebook</span> </a><a href="https://twitter.com/intent/tweet?url=https%3A%2F%2Fmelodyze.ai%2Fblog%2Fanother-post%2F&amp;via=Melodyze&amp;text=Ghost%20blog%20setup" class="js-share twitter" rel="nofollow noopener noreferrer"><svg class="icon" aria-hidden="true" focusable="false"><use xlink:href="https://melodyze.ai/blog/assets/svg/svg-map.svg#twitter"/></svg> <span>Twitter</span> </a><a href="https://www.linkedin.com/sharing/share-offsite/?url=https%3A%2F%2Fmelodyze.ai%2Fblog%2Fanother-post%2F" class="js-share linkedin" rel="nofollow noopener noreferrer"><svg class="icon" aria-hidden="true" focusable="false"><use xlink:href="https://melodyze.ai/blog/assets/svg/svg-map.svg#linkedin"/></svg> <span>LinkedIn</span> </a><a href="https://api.whatsapp.com/send?text=Ghost%20blog%20setup https%3A%2F%2Fmelodyze.ai%2Fblog%2Fanother-post%2F" class="js-share whatsapp" rel="nofollow noopener noreferrer"><svg class="icon" aria-hidden="true" focusable="false"><use xlink:href="https://melodyze.ai/blog/assets/svg/svg-map.svg#whatsapp"/></svg> <span>WhatsApp</span></a></div></div></div><div class="content__bio bio"><img src="https://www.gravatar.com/avatar/c4cd04d21360f8cb7a818518e494fd76?s&#x3D;240" loading="lazy" height="240" width="240" class="bio__avatar" alt=""><div><h3 class="h4 bio__name"><a href="https://melodyze.ai/blog/authors/sp4rx/" rel="author">Sp4Rx</a></h3></div></div></div><nav class="content__nav"><div class="wrapper"><div class="content__nav-inner"><div class="content__nav-prev"><a href="https://melodyze.ai/blog/post-2/" class="content__nav-link" rel="prev"><div><span>Previous</span> Hakintosh guide</div></a></div><div class="content__nav-next"><a href="https://melodyze.ai/blog/firebase/" class="content__nav-link" rel="next"><div><span>Next</span> Firebase</div></a></div></div></div></nav></footer></article><div class="content__related related"><div class="wrapper"><h2 class="h4 related__title">You should also read:</h2><article class="feed__item"><div class="feed__content"><header><div class="feed__meta"><img src="https://www.gravatar.com/avatar/c4cd04d21360f8cb7a818518e494fd76?s&#x3D;240" loading="lazy" height="240" width="240" class="feed__author-thumb" alt=""> <a href="https://melodyze.ai/blog/authors/sp4rx/" class="feed__author">Sp4Rx</a> <time datetime="2025-02-05T21:56" class="feed__date">February 5, 2025</time></div><h3 class="feed__title"><a href="https://melodyze.ai/blog/a-comprehensive-guide-to-testing-your-studio-setup-for-professional-sound/">A Comprehensive Guide to Testing Your Studio Setup for Professional Sound</a></h3></header><p>Before diving into a full recording session, it's essential to test your setup thoroughly. This ensures you capture high-quality audio, avoid unnecessary retakes, and save time in post-production. Testing your studio setup can seem like a small step, but it makes a huge difference in&hellip;</p><a href="https://melodyze.ai/blog/a-comprehensive-guide-to-testing-your-studio-setup-for-professional-sound/" class="readmore feed__readmore">Continue reading...</a></div></article></div></div></main><footer class="footer footer--glued"><div class="wrapper"><div class="footer__copyright"><p>© 2025 Melodyze.ai. All Rights Reserved. | Privacy Policy | Terms of Service | Contact Us<br>Follow us: Facebook | Twitter | Instagram | LinkedIn<br><br>Discover the future of music with Melodyze.ai</p></div><div class="footer__social"></div><button onclick="backToTopFunction()" id="backToTop" class="footer__bttop" aria-label="Back to top" title="Back to top"><svg width="20" height="20"><use xlink:href="https://melodyze.ai/blog/assets/svg/svg-map.svg#toparrow"/></svg></button></div></footer><script defer="defer" src="https://melodyze.ai/blog/assets/js/scripts.min.js?v=700105c316933a8202041b6415abb233"></script><script>window.publiiThemeMenuConfig={mobileMenuMode:'sidebar',animationSpeed:300,submenuWidth: 'auto',doubleClickTime:500,mobileMenuExpandableSubmenus:true,relatedContainerForOverlayMenuSelector:'.top'};</script><script>var images = document.querySelectorAll('img[loading]');
        for (var i = 0; i < images.length; i++) {
            if (images[i].complete) {
                images[i].classList.add('is-loaded');
            } else {
                images[i].addEventListener('load', function () {
                    this.classList.add('is-loaded');
                }, false);
            }
        }</script><div class="pcb" data-behaviour="badge" data-behaviour-link="#cookie-settings" data-revision="1" data-config-ttl="90" data-debug-mode="false"><div role="dialog" aria-modal="true" aria-hidden="true" aria-labelledby="pcb-title" aria-describedby="pcb-txt" class="pcb__banner"><div class="pcb__inner"><div id="pcb-title" role="heading" aria-level="2" class="pcb__title">This website uses cookies</div><div id="pcb-txt" class="pcb__txt">Select which cookies to opt-in to via the checkboxes below; our website uses cookies to examine site traffic and user activity while on our site, for marketing, and to provide social media functionality. <a href="https://melodyze.ai/privacy-policy">More details...</a></div><div class="pcb__buttons"><button type="button" class="pcb__btn pcb__btn--link pcb__btn--configure" aria-haspopup="dialog">Manage preferences</button> <button type="button" class="pcb__btn pcb__btn--solid pcb__btn--accept">Accept all</button></div></div></div><div class="pcb__popup" role="dialog" aria-modal="true" aria-hidden="true" aria-labelledby="pcb-popup-title"><div class="pcb__popup__wrapper"><div class="pcb__inner pcb__popup__inner"><div class="pcb__popup__heading"><div id="pcb-popup-title" role="heading" aria-level="2" class="pcb__title">Cookie settings</div><button class="pcb__popup__close" aria-label="Close"></button></div><div class="pcb__popup__content"><div class="pcb__txt pcb__popup__txt">We use cookies to enhance your browsing experience, serve personalized ads or content, and analyze our traffic. By clicking "Accept All", you consent to our use of cookies. <a href="https://melodyze.ai/privacy-policy">More details...</a></div><ul class="pcb__groups"><li class="pcb__group"><details><summary class="pcb__group__title no-desc">Required</summary></details><div class="pcb__popup__switch is-checked"><input type="checkbox" data-group-name="" id="pcb-group-0" checked="checked"> <label for="pcb-group-0">Required</label></div></li></ul></div><div class="pcb__buttons pcb__popup__buttons"><button type="button" class="pcb__btn pcb__btn--solid pcb__btn--accept">Accept all</button> <button type="button" class="pcb__btn pcb__btn--reject">Reject all</button> <button type="button" class="pcb__btn pcb__btn--save">Save settings</button></div></div></div></div><div class="pcb__overlay" aria-hidden="true"></div><button class="pcb__badge" aria-label="Cookie Policy" aria-hidden="true"><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" width="40" height="40" viewBox="0 0 23 23" fill="currentColor"><path d="M21.41 12.71c-.08-.01-.15 0-.22 0h-.03c-.03 0-.05 0-.08.01-.07 0-.13.01-.19.04-.52.21-1.44.19-2.02-.22-.44-.31-.65-.83-.62-1.53a.758.758 0 0 0-.27-.61.73.73 0 0 0-.65-.14c-1.98.51-3.49.23-4.26-.78-.82-1.08-.73-2.89.24-4.49.14-.23.14-.52 0-.75a.756.756 0 0 0-.67-.36c-.64.03-1.11-.1-1.31-.35-.19-.26-.13-.71-.01-1.29.04-.18.06-.38.03-.59-.05-.4-.4-.7-.81-.66C5.1 1.54 1 6.04 1 11.48 1 17.28 5.75 22 11.6 22c5.02 0 9.39-3.54 10.39-8.42.08-.4-.18-.78-.58-.87Zm-9.81 7.82c-5.03 0-9.12-4.06-9.12-9.06 0-4.34 3.05-8 7.25-8.86-.08.7.05 1.33.42 1.81.24.32.66.67 1.38.84-.76 1.86-.65 3.78.36 5.11.61.81 2.03 2 4.95 1.51.18.96.71 1.54 1.18 1.87.62.43 1.38.62 2.1.62.05 0 .09 0 .13-.01-1.23 3.64-4.7 6.18-8.64 6.18ZM13 17c0 .55-.45 1-1 1s-1-.45-1-1 .45-1 1-1 1 .45 1 1Zm5.29-12.3a.99.99 0 0 1-.29-.71c0-.55.45-.99 1-.99a1 1 0 0 1 .71.3c.19.19.29.44.29.71 0 .55-.45.99-1 .99a1 1 0 0 1-.71-.3ZM9 13.5c0 .83-.67 1.5-1.5 1.5S6 14.33 6 13.5 6.67 12 7.5 12s1.5.67 1.5 1.5Zm3.25.81a.744.744 0 0 1-.06-1.05c.28-.32.75-.34 1.05-.***********.75.05 1.06-.15.16-.35.25-.56.25-.18 0-.36-.06-.5-.19ZM8.68 7.26c.41.37.44 1 .07 1.41-.2.22-.47.33-.75.33a.96.96 0 0 1-.67-.26c-.41-.37-.44-1-.07-1.41.37-.42 1-.45 1.41-.08Zm11.48 1.88c.18-.19.52-.19.7 0 .**********.***********.*********** 0 .13-.05.26-.15.35-.09.1-.22.15-.35.15s-.26-.05-.35-.15a.355.355 0 0 1-.11-.16.433.433 0 0 1-.04-.19c0-.13.05-.26.15-.35Zm-4.93-1.86a.75.75 0 1 1 1.059-********** 0 0 1-1.059 1.06Z"/></svg></button></div><script>(function(win) {
    if (!document.querySelector('.pcb')) {
        return;
    }

    var cbConfig = {
        behaviour: document.querySelector('.pcb').getAttribute('data-behaviour'),
        behaviourLink: document.querySelector('.pcb').getAttribute('data-behaviour-link'),
        revision: document.querySelector('.pcb').getAttribute('data-revision'),
        configTTL: parseInt(document.querySelector('.pcb').getAttribute('data-config-ttl'), 10),
        debugMode: document.querySelector('.pcb').getAttribute('data-debug-mode') === 'true',
        initialState: null,
        initialLsState: null,
        previouslyAccepted: []
    };

    var cbUI = {
        wrapper: document.querySelector('.pcb'),
        banner: {
            element: null,
            btnAccept: null,
            btnReject: null,
            btnConfigure: null
        },
        popup: {
            element: null,
            btnClose: null,
            btnSave: null,
            btnAccept: null,
            btnReject: null,
            checkboxes: null,
        },
        overlay: null,
        badge: null,
        blockedScripts: document.querySelectorAll('script[type^="gdpr-blocker/"]'),
        triggerLinks: cbConfig.behaviourLink ? document.querySelectorAll('a[href*="' + cbConfig.behaviourLink + '"]') : null
    };

    function initUI () {
        // setup banner elements
        cbUI.banner.element = cbUI.wrapper.querySelector('.pcb__banner');
        cbUI.banner.btnAccept = cbUI.banner.element.querySelector('.pcb__btn--accept');
        cbUI.banner.btnReject = cbUI.banner.element.querySelector('.pcb__btn--reject');
        cbUI.banner.btnConfigure = cbUI.banner.element.querySelector('.pcb__btn--configure');

        // setup popup elements
        if (cbUI.wrapper.querySelector('.pcb__popup')) {
            cbUI.popup.element = cbUI.wrapper.querySelector('.pcb__popup');
            cbUI.popup.btnClose = cbUI.wrapper.querySelector('.pcb__popup__close');
            cbUI.popup.btnSave = cbUI.popup.element.querySelector('.pcb__btn--save');
            cbUI.popup.btnAccept = cbUI.popup.element.querySelector('.pcb__btn--accept');
            cbUI.popup.btnReject = cbUI.popup.element.querySelector('.pcb__btn--reject');
            cbUI.popup.checkboxes = cbUI.popup.element.querySelector('input[type="checkbox"]');
            // setup overlay
            cbUI.overlay = cbUI.wrapper.querySelector('.pcb__overlay');
        }

        cbUI.badge = cbUI.wrapper.querySelector('.pcb__badge');

        if (cbConfig.behaviour.indexOf('link') > -1) {
            for (var i = 0; i < cbUI.triggerLinks.length; i++) {
                cbUI.triggerLinks[i].addEventListener('click', function(e) {
                    e.preventDefault();
                    showBannerOrPopup();
                });
            }
        }
    }

    function initState () {
        var lsKeyName = getConfigName();
        var currentConfig = localStorage.getItem(lsKeyName);
        var configIsFresh = checkIfConfigIsFresh();

        if (!configIsFresh || currentConfig === null) {
            if (cbConfig.debugMode) {
                console.log('🍪 Config not found, or configuration expired');
            }

            if (window.publiiCBGCM) {
                gtag('consent', 'default', {
                    'ad_storage': window.publiiCBGCM.defaultState.ad_storage ? 'granted' : 'denied',
                    'ad_personalization': window.publiiCBGCM.defaultState.ad_personalization ? 'granted' : 'denied',
                    'ad_user_data': window.publiiCBGCM.defaultState.ad_user_data ? 'granted' : 'denied',
                    'analytics_storage': window.publiiCBGCM.defaultState.analytics_storage ? 'granted' : 'denied',
                    'personalization_storage': window.publiiCBGCM.defaultState.personalization_storage ? 'granted' : 'denied',
                    'functionality_storage': window.publiiCBGCM.defaultState.functionality_storage ? 'granted' : 'denied',
                    'security_storage': window.publiiCBGCM.defaultState.security_storage ? 'granted' : 'denied'
                });  
                
                if (cbConfig.debugMode) {
                    console.log('🍪 GCMv2 DEFAULT STATE: ' + JSON.stringify({
                        'ad_storage': window.publiiCBGCM.defaultState.ad_storage ? 'granted' : 'denied',
                        'ad_personalization': window.publiiCBGCM.defaultState.ad_personalization ? 'granted' : 'denied',
                        'ad_user_data': window.publiiCBGCM.defaultState.ad_user_data ? 'granted' : 'denied',
                        'analytics_storage': window.publiiCBGCM.defaultState.analytics_storage ? 'granted' : 'denied',
                        'personalization_storage': window.publiiCBGCM.defaultState.personalization_storage ? 'granted' : 'denied',
                        'functionality_storage': window.publiiCBGCM.defaultState.functionality_storage ? 'granted' : 'denied',
                        'security_storage': window.publiiCBGCM.defaultState.security_storage ? 'granted' : 'denied'
                    }));
                }
            }

            showBanner();
        } else if (typeof currentConfig === 'string') {
            if (cbConfig.debugMode) {
                console.log('🍪 Config founded');
            }

            cbConfig.initialLsState = currentConfig.split(',');

            if (window.publiiCBGCM) {
                gtag('consent', 'default', {
                    'ad_storage': getDefaultConsentState(currentConfig, 'ad_storage'),
                    'ad_personalization': getDefaultConsentState(currentConfig, 'ad_personalization'),
                    'ad_user_data': getDefaultConsentState(currentConfig, 'ad_user_data'),
                    'analytics_storage': getDefaultConsentState(currentConfig, 'analytics_storage'),
                    'personalization_storage': getDefaultConsentState(currentConfig, 'personalization_storage'),
                    'functionality_storage': getDefaultConsentState(currentConfig, 'functionality_storage'),
                    'security_storage': getDefaultConsentState(currentConfig, 'security_storage')
                });
                
                if (cbConfig.debugMode) {
                    console.log('🍪 GCMv2 DEFAULT STATE: ' + JSON.stringify({
                        'ad_storage': getDefaultConsentState(currentConfig, 'ad_storage'),
                        'ad_personalization': getDefaultConsentState(currentConfig, 'ad_personalization'),
                        'ad_user_data': getDefaultConsentState(currentConfig, 'ad_user_data'),
                        'analytics_storage': getDefaultConsentState(currentConfig, 'analytics_storage'),
                        'personalization_storage': getDefaultConsentState(currentConfig, 'personalization_storage'),
                        'functionality_storage': getDefaultConsentState(currentConfig, 'functionality_storage'),
                        'security_storage': getDefaultConsentState(currentConfig, 'security_storage')
                    }));
                }
            }

            showBadge();

            if (cbUI.popup.element) {
                var allowedGroups = currentConfig.split(',');
                var checkedCheckboxes = cbUI.popup.element.querySelectorAll('input[type="checkbox"]:checked');

                for (var j = 0; j < checkedCheckboxes.length; j++) {
                    var name = checkedCheckboxes[j].getAttribute('data-group-name');

                    if (name && name !== '-' && allowedGroups.indexOf(name) === -1) {
                        checkedCheckboxes[j].checked = false;
                    }
                }

                for (var i = 0; i < allowedGroups.length; i++) {
                    var checkbox = cbUI.popup.element.querySelector('input[type="checkbox"][data-group-name="' + allowedGroups[i] + '"]');

                    if (checkbox) {
                        checkbox.checked = true;
                    }

                    allowCookieGroup(allowedGroups[i]);
                }
            }
        }

        setTimeout(function () {
            cbConfig.initialState = getInitialStateOfConsents();
        }, 0);
    }

    function checkIfConfigIsFresh () {
        var lastConfigSave = localStorage.getItem('publii-gdpr-cookies-config-save-date');

        if (lastConfigSave === null) {
            return false;
        }

        lastConfigSave = parseInt(lastConfigSave, 10);

        if (lastConfigSave === 0) {
            return true;
        }

        if (+new Date() - lastConfigSave < cbConfig.configTTL * 24 * 60 * 60 * 1000) {
            return true;
        }

        return false;
    }

    function getDefaultConsentState (currentConfig, consentGroup) {
        let configGroups = currentConfig.split(',');

        for (let i = 0; i < configGroups.length; i++) {
            let groupName = configGroups[i];
            let group = window.publiiCBGCM.groups.find(group => group.cookieGroup === groupName);

            if (group && group[consentGroup]) {
                return 'granted';
            }
        }  
        
        if (window.publiiCBGCM.defaultState[consentGroup]) {
            return 'granted'; 
        }
        
        return 'denied';
    }

    function initBannerEvents () {
        cbUI.banner.btnAccept.addEventListener('click', function (e) {
            e.preventDefault();
            acceptAllCookies('banner');
            showBadge();
        }, false);

        if (cbUI.banner.btnReject) {
            cbUI.banner.btnReject.addEventListener('click', function (e) {
                e.preventDefault();
                rejectAllCookies();
                showBadge();
            }, false);
        }

        if (cbUI.banner.btnConfigure) {
            cbUI.banner.btnConfigure.addEventListener('click', function (e) {
                e.preventDefault();
                hideBanner();
                showAdvancedPopup();
                showBadge();
            }, false);
        }
    }

    function initPopupEvents () {
        if (!cbUI.popup.element) {
            return;
        }

        cbUI.overlay.addEventListener('click', function (e) {
            hideAdvancedPopup();
        }, false);

        cbUI.popup.element.addEventListener('click', function (e) {
            e.stopPropagation();
        }, false);

        cbUI.popup.btnAccept.addEventListener('click', function (e) {
            e.preventDefault();
            acceptAllCookies('popup');
        }, false);

        cbUI.popup.btnReject.addEventListener('click', function (e) {
            e.preventDefault();
            rejectAllCookies();
        }, false);

        cbUI.popup.btnSave.addEventListener('click', function (e) {
            e.preventDefault();
            saveConfiguration();
        }, false);

        cbUI.popup.btnClose.addEventListener('click', function (e) {
            e.preventDefault();
            hideAdvancedPopup();
        }, false);
    }

    function initBadgeEvents () {
        if (!cbUI.badge) {
            return;
        }

        cbUI.badge.addEventListener('click', function (e) {
            showBannerOrPopup();
        }, false);
    }

    initUI();
    initState();
    initBannerEvents();
    initPopupEvents();
    initBadgeEvents();

    /**
     * API
     */
    function addScript (src, inline) {
        var newScript = document.createElement('script');

        if (src) {
            newScript.setAttribute('src', src);
        }

        if (inline) {
            newScript.text = inline;
        }

        document.body.appendChild(newScript);
    }

    function allowCookieGroup (allowedGroup) {
        var scripts = document.querySelectorAll('script[type="gdpr-blocker/' + allowedGroup + '"]');
        cbConfig.previouslyAccepted.push(allowedGroup);
    
        for (var j = 0; j < scripts.length; j++) {
            addScript(scripts[j].src, scripts[j].text);
        }

        var groupEvent = new Event('publii-cookie-banner-unblock-' + allowedGroup);
        document.body.dispatchEvent(groupEvent);
        unlockEmbeds(allowedGroup);

        if (cbConfig.debugMode) {
            console.log('🍪 Allowed group: ' + allowedGroup);
        }

        if (window.publiiCBGCM && cbConfig.initialLsState.indexOf(allowedGroup) === -1) {
            let consentResult = {};
            let group = window.publiiCBGCM.groups.find(group => group.cookieGroup === allowedGroup);

            if (group) {
                let foundSomeConsents = false;

                Object.keys(group).forEach(key => {
                    if (key !== 'cookieGroup' && group[key] === true) {
                        consentResult[key] = 'granted';
                        foundSomeConsents = true;
                    }
                });

                if (foundSomeConsents) {
                    gtag('consent', 'update', consentResult);   

                    if (cbConfig.debugMode) {
                        console.log('🍪 GCMv2 UPDATE: ' + JSON.stringify(consentResult));
                    }
                }
            }
        }
    }

    function showBannerOrPopup () {
        if (cbUI.popup.element) {
            showAdvancedPopup();
        } else {
            showBanner();
        }
    }

    function showAdvancedPopup () {
        cbUI.popup.element.classList.add('is-visible');
        cbUI.overlay.classList.add('is-visible');
        cbUI.popup.element.setAttribute('aria-hidden', 'false');
        cbUI.overlay.setAttribute('aria-hidden', 'false');
    }

    function hideAdvancedPopup () {
        cbUI.popup.element.classList.remove('is-visible');
        cbUI.overlay.classList.remove('is-visible');
        cbUI.popup.element.setAttribute('aria-hidden', 'true');
        cbUI.overlay.setAttribute('aria-hidden', 'true');
    }

    function showBanner () {
        cbUI.banner.element.classList.add('is-visible');
        cbUI.banner.element.setAttribute('aria-hidden', 'false');
    }

    function hideBanner () {
        cbUI.banner.element.classList.remove('is-visible');
        cbUI.banner.element.setAttribute('aria-hidden', 'true');
    }

    function showBadge () {
        if (!cbUI.badge) {
            return;
        }

        cbUI.badge.classList.add('is-visible');
        cbUI.badge.setAttribute('aria-hidden', 'false');
    }

    function getConfigName () {
        var lsKeyName = 'publii-gdpr-allowed-cookies';

        if (cbConfig.revision) {
            lsKeyName = lsKeyName + '-v' + parseInt(cbConfig.revision, 10);
        }

        return lsKeyName;
    }

    function storeConfiguration (allowedGroups) {
        var lsKeyName = getConfigName();
        var dataToStore = allowedGroups.join(',');
        localStorage.setItem(lsKeyName, dataToStore);

        if (cbConfig.configTTL === 0) {
            localStorage.setItem('publii-gdpr-cookies-config-save-date', 0);

            if (cbConfig.debugMode) {
                console.log('🍪 Store never expiring configuration');
            }
        } else {
            localStorage.setItem('publii-gdpr-cookies-config-save-date', +new Date());
        }
    }

    function getInitialStateOfConsents () {
        if (!cbUI.popup.element) {
            return [];
        }

        var checkedGroups = cbUI.popup.element.querySelectorAll('input[type="checkbox"]:checked');
        var groups = [];

        for (var i = 0; i < checkedGroups.length; i++) {
            var allowedGroup = checkedGroups[i].getAttribute('data-group-name');

            if (allowedGroup !== '') {
                groups.push(allowedGroup);
            }
        }

        if (cbConfig.debugMode) {
            console.log('🍪 Initial state: ' + groups.join(', '));
        }

        return groups;
    }

    function getCurrentStateOfConsents () {
        if (!cbUI.popup.element) {
            return [];
        }

        var checkedGroups = cbUI.popup.element.querySelectorAll('input[type="checkbox"]:checked');
        var groups = [];

        for (var i = 0; i < checkedGroups.length; i++) {
            var allowedGroup = checkedGroups[i].getAttribute('data-group-name');

            if (allowedGroup !== '') {
                groups.push(allowedGroup);
            }
        }

        if (cbConfig.debugMode) {
            console.log('🍪 State to save: ' + groups.join(', '));
        }

        return groups;
    }

    function getAllGroups () {
        if (!cbUI.popup.element) {
            return [];
        }

        var checkedGroups = cbUI.popup.element.querySelectorAll('input[type="checkbox"]');
        var groups = [];

        for (var i = 0; i < checkedGroups.length; i++) {
            var allowedGroup = checkedGroups[i].getAttribute('data-group-name');

            if (allowedGroup !== '') {
                groups.push(allowedGroup);
            }
        }

        return groups;
    }

    function acceptAllCookies (source) {
        var groupsToAccept = getAllGroups();
        storeConfiguration(groupsToAccept);

        for (var i = 0; i < groupsToAccept.length; i++) {
            var group = groupsToAccept[i];

            if (cbConfig.initialState.indexOf(group) > -1 || cbConfig.previouslyAccepted.indexOf(group) > -1) {
                if (cbConfig.debugMode) {
                    console.log('🍪 Skip previously activated group: ' + group);
                }

                continue;
            }

            allowCookieGroup(group);
        }

        if (cbUI.popup.element) {
            var checkboxesToCheck = cbUI.popup.element.querySelectorAll('input[type="checkbox"]');

            for (var j = 0; j < checkboxesToCheck.length; j++) {
                checkboxesToCheck[j].checked = true;
            }
        }

        if (cbConfig.debugMode) {
            console.log('🍪 Accept all cookies: ', groupsToAccept.join(', '));
        }

        if (source === 'popup') {
            hideAdvancedPopup();
        } else if (source === 'banner') {
            hideBanner();
        }
    }

    function rejectAllCookies () {
        if (cbConfig.debugMode) {
            console.log('🍪 Reject all cookies');
        }

        storeConfiguration([]);
        setTimeout(function () {
            window.location.reload();
        }, 100);
    }

    function saveConfiguration () {
        var groupsToAccept = getCurrentStateOfConsents();
        storeConfiguration(groupsToAccept);

        if (cbConfig.debugMode) {
            console.log('🍪 Save new config: ', groupsToAccept.join(', '));
        }

        if (reloadIsNeeded(groupsToAccept)) {
            setTimeout(function () {
                window.location.reload();
            }, 100);
            return;
        }

        for (var i = 0; i < groupsToAccept.length; i++) {
            var group = groupsToAccept[i];

            if (cbConfig.initialState.indexOf(group) > -1 || cbConfig.previouslyAccepted.indexOf(group) > -1) {
                if (cbConfig.debugMode) {
                    console.log('🍪 Skip previously activated group: ' + group);
                }

                continue;
            }

            allowCookieGroup(group);
        }

        hideAdvancedPopup();
    }

    function reloadIsNeeded (groupsToAccept) {
        // check if user rejected consent for initial groups
        var initialGroups = cbConfig.initialState;
        var previouslyAcceptedGroups = cbConfig.previouslyAccepted;
        var groupsToCheck = initialGroups.concat(previouslyAcceptedGroups);

        for (var i = 0; i < groupsToCheck.length; i++) {
            var groupToCheck = groupsToCheck[i];

            if (groupToCheck !== '' && groupsToAccept.indexOf(groupToCheck) === -1) {
                if (cbConfig.debugMode) {
                    console.log('🍪 Reload is needed due lack of: ', groupToCheck);
                }

                return true;
            }
        }

        return false;
    }

    function unlockEmbeds (cookieGroup) {
        var iframesToUnlock = document.querySelectorAll('.pec-wrapper[data-consent-group-id="' + cookieGroup + '"]');

        for (var i = 0; i < iframesToUnlock.length; i++) {
            var iframeWrapper = iframesToUnlock[i];
            iframeWrapper.querySelector('.pec-overlay').classList.remove('is-active');
            iframeWrapper.querySelector('.pec-overlay').setAttribute('aria-hidden', 'true');
            var iframe = iframeWrapper.querySelector('iframe');
            iframe.setAttribute('src', iframe.getAttribute('data-consent-src'));
        }
    }

    win.publiiEmbedConsentGiven = function (cookieGroup) {
        // it will unlock embeds
        allowCookieGroup(cookieGroup);

        var checkbox = cbUI.popup.element.querySelector('input[type="checkbox"][data-group-name="' + cookieGroup + '"]');

        if (checkbox) {
            checkbox.checked = true;
        }

        var groupsToAccept = getCurrentStateOfConsents();
        storeConfiguration(groupsToAccept);

        if (cbConfig.debugMode) {
            console.log('🍪 Save new config: ', groupsToAccept.join(', '));
        }
    }
})(window);</script></body></html>