/* Loop Video CSS START */
@media (max-width:1367px) {
    .main-icons .top-icon1 {
        width: 37%;
        height: 70%;
        margin-left: 4%;
    }

    .main-icons .top-icon2 {
        width: 37%;
        height: 70%;
        right: 6%;

    }

    .main-icons1 .top-icon3 {
        width: 37%;
        height: 70%;
        margin-bottom: 3%;
        margin-left: 4%;
    }

    .main-icons1 .top-icon4 {
        width: 37%;
        height: 70%;
        right: 5%;
        margin-bottom: 4%;
    }
}

@media (max-width:1180px) {
    .main-icons .top-icon1 {
        width: 37%;
        height: 70%;
        margin-left: 4%;
    }

    .main-icons .top-icon2 {
        width: 37%;
        height: 70%;
        right: 6%;

    }

    .main-icons1 .top-icon3 {
        width: 37%;
        height: 70%;
        margin-bottom: 3%;
        margin-left: 4%;
    }

    .main-icons1 .top-icon4 {
        width: 37%;
        height: 70%;
        right: 5%;
        margin-bottom: 3%;
    }
}

@media (max-width:1050px) {
    .main-icons .top-icon1 {
        width: 37%;
        height: 70%;
        margin-left: 4%;
    }

    .main-icons .top-icon2 {
        width: 37%;
        height: 70%;
        right: 6%;

    }

    .main-icons1 .top-icon3 {
        width: 37%;
        height: 70%;
        margin-bottom: 3%;
        margin-left: 4%;
    }

    .main-icons1 .top-icon4 {
        width: 37%;
        height: 70%;
        right: 5%;
        margin-bottom: 3%;
    }
}

@media (max-width:1025px) {
    .main-icons .top-icon1 {
        width: 37%;
        height: 70%;
        margin-left: 4%;
    }

    .main-icons .top-icon2 {
        width: 37%;
        height: 70%;
        right: 5%;

    }

    .main-icons1 .top-icon3 {
        width: 37%;
        height: 70%;
        margin-bottom: 5%;
        margin-left: 4%;
    }

    .main-icons1 .top-icon4 {
        width: 37%;
        height: 70%;
        right: 5%;
        margin-bottom: 6%;
    }
}

@media (max-width:916px) {
    .main-icons .top-icon1 {
        width: 37%;
        height: 70%;
        margin-left: 4%;
    }

    .main-icons .top-icon2 {
        width: 37%;
        height: 70%;
        right: 5%;

    }

    .main-icons1 .top-icon3 {
        width: 37%;
        height: 70%;
        margin-bottom: 2%;
        margin-left: 2.5%;
    }

    .main-icons1 .top-icon4 {
        width: 37%;
        height: 70%;
        right: 5%;
        margin-bottom: 2%;
    }
}

@media (max-width:913px) {
    .main-icons .top-icon1 {
        width: 37%;
        height: 70%;
        margin-left: 4%;
    }

    .main-icons .top-icon2 {
        width: 37%;
        height: 70%;
        right: 5%;

    }

    .main-icons1 .top-icon3 {
        width: 37%;
        height: 70%;
        margin-bottom: 6%;
        margin-left: 2.5%;
    }

    .main-icons1 .top-icon4 {
        width: 37%;
        height: 70%;
        right: 5%;
        margin-bottom: 6.5%;
    }
}

@media (max-width:845px) {
    .main-icons .top-icon1 {
        width: 34%;
        height: 70%;
        margin-left: 5%;
    }

    .main-icons .top-icon2 {
        width: 34%;
        height: 70%;
        right: 6%;

    }

    .main-icons1 .top-icon3 {
        width: 34%;
        height: 70%;
        margin-bottom: 2.5%;
        margin-left: 5%;
    }

    .main-icons1 .top-icon4 {
        width: 34%;
        height: 70%;
        right: 6%;
        margin-bottom: 2.5%;
    }
}

@media (max-width:821px) {
    .main-icons .top-icon1 {
        width: 34%;
        height: 70%;
        margin-left: 5%;
        margin-top: 3%;
    }

    .main-icons .top-icon2 {
        width: 34%;
        height: 70%;
        right: 6%;
        margin-top: 3%;

    }

    .main-icons1 .top-icon3 {
        width: 34%;
        height: 70%;
        margin-bottom: 8%;
        margin-left: 5%;
    }

    .main-icons1 .top-icon4 {
        width: 34%;
        height: 70%;
        right: 6%;
        margin-bottom: 8%;
    }
}

@media (max-width:768px) {
    .main-icons .top-icon1 {
        width: 37%;
        height: 70%;
        margin-left: 4%;
    }

    .main-icons .top-icon2 {
        width: 37%;
        height: 70%;
        right: 5%;

    }

    .main-icons1 .top-icon3 {
        width: 37%;
        height: 70%;
        margin-bottom: 5%;
        margin-left: 3%;
    }

    .main-icons1 .top-icon4 {
        width: 37%;
        height: 70%;
        right: 5%;
        margin-bottom: 6%;
    }
}

@media (max-width:700px) {
    .main-icons .top-icon1 {
        width: 37%;
        height: 70%;
        margin-left: 4%;
    }

    .main-icons .top-icon2 {
        width: 37%;
        height: 70%;
        right: 5%;

    }

    .main-icons1 .top-icon3 {
        width: 37%;
        height: 70%;
        margin-bottom: 5%;
        margin-left: 3%;
    }

    .main-icons1 .top-icon4 {
        width: 37%;
        height: 70%;
        right: 5%;
        margin-bottom: 6%;
    }
}

@media (max-width:500px) {
    #menudisplay .intro-vdo video {
        display: none;
    }

    #menudisplay .intro-vdo1 video {
        display: flex;
    }
    .main-icons .top-icon1 {
        width: 55%;
        height: 34%;
        margin-left: 0%;
        margin-top: 8vh;

    }

    .main-icons .top-icon2 {
        width: 55%;
        height: 34%;
        right: 2%;
        margin-top: 8vh;

    }

    .main-icons1 .top-icon3 {
        width: 55%;
        height: 34%;
        margin-bottom: 16vh;
        margin-left: 0%;
    }

    .main-icons1 .top-icon4 {
        width: 55%;
        height: 34%;
        margin-bottom: 16vh;
        right: 1.5%;
    }


}




/* Loop Video CSS END */

/* Singup CSS START */

/* Singup CSS END */


/* Social links CSS Start */
@media (max-width:920px) {

    .social h1 {
        font-size: 35px;

    }

    .social-links img {
        transition: 0.5s ease-in;
        width: 90px;
        border-radius: 50%;
    }

    .social-links img:hover {
        width: 100px;
    }
}

@media (max-width:650px) {

    .social h1 {
        font-size: 30px;

    }

    .social-links img {
        width: 60px;
    }

    .social-links img:hover {
        width: 70px;
    }
}

@media (max-width:450px) {
    .social {
        width: 100%;
        height: 20%;
        margin-top: 50%;
    }

    .social h1 {
        font-size: 25px;

    }

    .social-links img {
        width: 40px;
    }

    .social-links img:hover {
        width: 50px;
    }

    .close-button4 {
        font-size: 22px;
        width: 40px;
        height: 40px;
    }
}

@media (max-width:350px) {
    .social {
        width: 100%;
        height: 10%;
        margin-top: 80%;
    }

    .social h1 {
        font-size: 20px;

    }

    .social-links img {
        width: 35px;
    }

    .social-links img:hover {
        width: 38px;
    }
}

/* Social links CSS END */