@import url("https://fonts.googleapis.com/css2?family=Share+Tech+Mono&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Montserrat&display=swap");
* {
  padding: 0;
  margin: 0;
  box-sizing: border-box;
}
html {
  font-family: "Share Tech Mono", monospace;
}
.max-scrn {
  margin: auto;
  max-width: 2000px;
  width: 100%;
}
/* Intro video css Start */
.intro-main {
  position: relative;
  width: 100%;
  height: 100vh;
}

.video-wrapper {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
}
.video-wrapper video {
  width: 100%;
  height: 100%;
  object-fit: fill;
  position: fixed;
  max-width: 2000px;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  /* z-index: -999999999; */
}
/* Intro video css End */

/* intro video buttons Start */
.my-control {
  max-width: 2000px;

  /* background-color: rgba(0, 0, 0, 0.453); */
  /* border: 2px solid rgba(255, 255, 255, 0.286); */
  bottom: -13px;
  left: 45%;
  padding: 10px;
  position: absolute;
  margin: auto;
  width: 150px;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: slide-in 0.5s forwards;
}
.my-control ul {
  list-style: none;
  /* display: flex; */
  margin: auto;
  align-items: center;
  justify-content: center;
}
.my-control ul .bg-gry {
  cursor: pointer;
  margin: auto;
  line-height: 78px;
  height: 65px;
  width: 65px;
  box-shadow: rgba(208, 208, 208, 0.24) 0px 3px 8px;
  border-radius: 50%;
  background-color: rgba(1, 247, 235, 0.695);
}
.my-control ul li {
  cursor: pointer;
  margin: 5px;
  line-height: 60px;
  font-weight: 700;
  height: 60px;
  width: 60px;
  text-align: center;
  color: white;
  font-family: "Montserrat", sans-serif;
  font-size: 16px;
  transition: 0.5s ease-in;
}
.my-control ul .bg-gry:hover {
  background-color: rgba(1, 247, 235, 0.836);
  transition: 0.5s ease-in;
}
.my-control ul li:hover {
  transform: scale(1.1);
  transition: 0.5s ease-in;
}
.my-control ul li img {
  width: 23px;
  height: 23px;
  margin-top: 15px;
}
#myDiv {
  position: fixed;
}

@keyframes slide-in {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translatey(0);
    opacity: 1;
  }
}

.hidden {
  display: none;
}
@media (max-width: 600px) {
  .my-control {
    width: 0;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
  }
}
/* intro video buttons End */

#menudisplay {
  /* border: 2px solid blue; */
  top: 0;
  margin: auto;
  left: 0;
  width: 100%;
  display: none;
  text-align: center;
  position: relative;
  overflow: hidden;
  overflow: -moz-hidden-unscrollable;
}
#menudisplay .intro-vdo {
  /* position:relative; */
  z-index: -999999999;
  margin: auto;
  max-width: 2000px;
  width: 100%;
  /* height: 100vh; */
  /* border: 2px solid rgb(255, 81, 0); */
}
#menudisplay .intro-vdo video {
  width: 100%;
  height: 100%;
  object-fit: fill;
  position: fixed;
  max-width: 2000px;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: -999999999;
}

/* Intro video for Mobile Start */
#menudisplay .intro-vdo1 {
  /* position:relative; */
  z-index: -999999999;
  margin: auto;
  object-fit: contain;
  max-width: 2000px;
  width: 100%;
  /* height: 100vh; */
  /* border: 2px solid rgb(255, 81, 0); */
}
#menudisplay .intro-vdo1 video {
  width: 100%;
  display: none;
  height: 100%;
  object-fit: fill;
  position: fixed;
  max-width: 2000px;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: -999999999;
}
/* Intro video for Mobile end */
.main-icons {
  top: 2%;
  position: fixed;
  height: 50%;
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  width: 100%;
  margin: auto;
  max-width: 2000px;
}
.main-icons .top-icon1 {
  position: absolute;
  width: 33%;
  height: 70%;
  margin-left: 5.5%;
  margin-top: 0.5%;
  cursor: pointer;
}

.main-icons .top-icon2 {
  position: absolute;
  width: 33%;
  height: 70%;
  right: 7%;
  margin-top: 0.5%;
  cursor: pointer;
}

/* .top-icon1:hover{
  cursor: pointer;
} */

/* .main-icons .top-icon1 img{
    width: 100%;
    height: 100%;
    object-fit: fill;
} */
/* .main-icons .top-icon2 img{
    width: 100%;
    height: 100%;
}
.main-icons img{
    transition: transform 1.5s ease-in;
}
.main-icons img:hover{
    cursor: pointer;
    opacity: 0.9;
    transform: scale(1.1);
} */

.main-icons1 {
  position: fixed;
  /* top: 5%; */
  height: 50%;
  display: flex;
  align-items: flex-end;
  justify-content: space-between;
  width: 100%;
  margin: auto;
  max-width: 2000px;
  bottom: 0;
}

.main-icons1 .top-icon3 {
  position: absolute;
  width: 33%;
  height: 70%;
  margin-left: 5.3%;
  margin-bottom: 2.2%;
  cursor: pointer;
}
.main-icons1 .top-icon4 {
  position: absolute;
  width: 33.5%;
  height: 70%;
  right: 6.5%;
  margin-bottom: 2.5%;
  cursor: pointer;
}

/* .main-icons1 .top-icon3 img{
    width: 100%;
    height: 100%;
} */
/* .main-icons1 .top-icon4 img{
    width: 100%;
    height: 100%;
} */
/* .main-icons1 img{
    transition: transform 1.5s ease-in;
}
.main-icons1 img:hover{
    cursor: pointer;
    opacity: 0.9;
    transform: scale(1.1);
} */

#bg-blck {
  position: absolute;
  z-index: 1;
  width: 100vw;
  display: none;
  height: 100vh;
  background-color: black;
  /* transition: all 1.5s ease-in-out; */
  opacity: 0;
  animation: fade-in 3s ease forwards;
  /* transition: opacity 0.5s ease; */
}
@keyframes fade-in {
  0% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}
/* 
@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
} */
#loadingDiv {
  display: flex;
  align-items: center;
  justify-content: center;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
}

.spinner {
  width: 50px;
  height: 50px;
  border: 5px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: #fff;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

#messageDiv {
  width: 100%;
  height: 95%;
  margin: auto; /* Reset margin */
  align-items: center;
  justify-content: center;
}
#messageDiv h1 {
  line-height: 1.5;
  font-size: 30px;
  text-align: left;
  color: aqua;
  font-size: 30px;
  text-transform: uppercase;
}
/* Overlay Start  */

/* Overlay 1 Start  */

.overlay {
  margin: auto;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  position: absolute;
  /* bottom: 0; */
  top: 100%;
  left: 0;
  /* Changed from 'right' to 'left' */
  width: 100%;
  height: 100vh;
  background-color: rgb(0, 0, 0);
  display: none;
  transform-origin: bottom;
  /* clip-path: circle(0% at 60% 110%); */
  clip-path: inset(0); /* Initially, clip the overlay to a square shape */
  transition: top 0.8s ease-in-out;
}

.overlay.active {
  top: 0;
  /* clip-path: circle(150% at 0% 0%); */
  clip-path: circle(100% at center);
}

.content {
  box-shadow: 0 0 10px 5px #48abe0;
  border: 1px solid #48abe0;
  /* background-color: rgba(109, 109, 109, 0.845); */
  padding: 20px;
  height: 80vh;
  width: 80%;
  text-align: center;
  align-items: center;
  justify-content: center;
  margin: 10vh auto;
  display: none;
  overflow: hidden;
  animation: slideFromLeft 1.9s forwards ease; /* Add animation property */
  opacity: 0; /* Start with opacity set to 0 */
}
.l-logo {
  position: absolute;
  width: 80px;
  margin: 10px;
}
.l-logo img {
  cursor: pointer;
  width: 100%;
}
@keyframes slideFromLeft {
  0% {
    opacity: 0;
    transform: translateY(100%);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}
.close-button {
  position: absolute;
  top: 10px;
  right: 10px;
  font-size: 35px;
  width: 50px;
  height: 50px;
  outline: none;
  border: none;
  color: white;
  border: 2px solid white;
  border-radius: 50%;
  cursor: pointer;
  background-color: transparent;
  transition: all 0.5s ease-in;
}
.close-button:hover {
  background-color: black;
  transition: all 0.5s ease-in;
}
/* Overlay 1 End  */

/* Overlay 2 Start  */
.overlay2 {
  font-family: "Share Tech Mono", monospace;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: none;
  justify-content: center;
  align-items: center;
  transform-origin: center center;
  overflow-x: auto;
  clip-path: circle(0% at 80% 10%);
  transition: clip-path 0.9s ease-in-out;
}

.overlay2.active {
  clip-path: circle(150% at 0% 0%);
}

.content2 {
  background-color: rgba(0, 0, 0, 0.815);
  padding: 5px;
  width: 80%;
  height: 100%;
  max-width: 100%;
  margin: auto;
  text-align: left;
  top: 50%;
  left: 50%;
}

.signup-r-side {
  width: 80%;
  padding: 10px;
  color: white;
  text-transform: uppercase;
  font-family: "Share Tech Mono", monospace;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin: auto;
}

.signup-r-side h1 {
  text-align: center;
  margin: 12px 0px 10px 0px;
}

#message {
  font-size: 20px;
  color: red;
  animation: messages 0.8s linear;
}
#message1 {
  border: 2px solid yellow;
  width: 100%;
  height: 100%;
  /* display: none; */
  font-size: 20px;
  color: red;
  animation: messages 0.8s linear;
}

@keyframes messages {
  0% {
    transform: scale(0.1);
    opacity: 0.1;
  }

  50% {
    transform: scale(0.5);
    opacity: 0.5;
  }

  100% {
    transform: scale(1);
    opacity: 0.1;
  }
}

form {
  width: 89%;
  max-width: 100%;
  padding: 5px;
  margin: auto;
}

.frm-names {
  margin: 15px auto;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.frm-names .fname,
.frm-names .lname {
  width: 48%;
  color: #cfd8e7;

}

form label {
  font-weight: 600;
  margin: 10px auto;
  font-size: 20px;
  font-family: "Share Tech Mono", monospace;
  text-align: left !important;
}

form label span,
.share-file h3 span {
  color: red;
  margin-left: 10px;
}

form input {
  font-family: "Share Tech Mono", monospace;
  width: 100%;
  height: 30px;
  margin: 10px auto;
  font-size: 18px;
  margin-bottom: 10px;
  outline: none;
  border: none;
  background-color: rgba(0, 0, 0, 0.119);
  font-weight: 500;
  color: rgb(255, 255, 255);
  border-radius: 3px;
  border-bottom: 3px solid rgb(90, 105, 165);
  border-color: rgba(var(--brde, 163, 217, 246), var(--alpha-brde, 1));
}

form input:hover {
  border-color: rgb(255, 255, 255);
}

.find-us,
.Uname {
  text-align: left;
  width: 100%;
  color: #cfd8e7;
}

.share-file {
  /* display: flex; */
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  text-align: left;
  margin: 5px auto;
  width: 100%;
}
.share-file h3 {
  margin: 15px auto;
  text-align: left;
  font-size: 22px;
}

.share-file .frm-names .more-space {
  text-align: center;
  width: 20%;
  font-size: 25px;
}

.share-file .link-share {
  margin: 15px auto;
  width: 100%;
}

form .btnsub {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: auto;
  outline: none;
  border: none;
  text-align: center;
  width: 100%;
  height: 50px;
  color: rgb(0, 0, 0);
  font-size: 20px;
  font-weight: 600;
  background-color: aquamarine;
  transition: 0.5s ease;
  font-family: "Montserrat", sans-serif !important;
}

form .btnsub:hover {
  cursor: pointer;
  transition: 0.5s ease;
  background-color: rgba(127, 255, 212, 0.823);
}

.close-button2 {
  position: absolute;
  top: 10px;
  right: 10px;
  font-size: 35px;
  width: 50px;
  height: 50px;
  outline: none;
  border: none;
  color: white;
  border: 2px solid white;
  border-radius: 50%;
  cursor: pointer;
  background-color: transparent;
  transition: all 0.5s ease-in;
}

.close-button2:hover {
  background-color: black;
  transition: all 0.5s ease-in;
}

input[type="file"]::-webkit-file-upload-button {
  font-size: 16px;
  color: white;
  background-color: #000000;
  border: none;
  border-radius: 4px;
  padding: 8px 16px;
  cursor: pointer;
}

input[type="file"]::-webkit-file-upload-button:hover {
  background-color: #0056b3;
}

/* Responsive Styles */
@media (max-width: 768px) {
  .frm-names {
    flex-direction: column;
  }

  .signup-r-side {
    width: 100%;
  }

  .frm-names .fname,
  .frm-names .lname {
    width: 100%;
  }

  .form input {
    width: 100%;
  }

  .form .btnsub {
    width: 100%;
  }

  .signup-r-side h1 {
    font-size: 22px;
  }

  form label {
    font-size: 15px;
  }

  input[type="file"]::-webkit-file-upload-button {
    font-size: 14px;
    padding: 8px 13px;
  }

  .share-file h3 {
    font-size: 18px;
  }
  .close-button2 {
    font-size: 35px;
    width: 45px;
    height: 45px;
  }
}

@media (max-width: 480px) {
  .content2 {
    width: 100%;
  }
  .signup-r-side {
    width: 100%;
  }

  form label {
    width: 100%;
    font-size: 13px;
  }

  form input {
    font-size: 16px;
  }

  .signup-r-side h1 {
    font-size: 18px;
  }

  input[type="file"]::-webkit-file-upload-button {
    font-size: 12px;
    padding: 5px 10px;
  }

  .share-file h3 {
    font-size: 12px;
  }

  .share-file .frm-names .more-space {
    font-size: 18px;
  }
  .close-button2 {
    font-size: 30px;
    width: 40px;
    height: 40px;
  }
}
/* Overlay 2End  */

/* overlay 3 start */
.overlay3 {
  margin: auto;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  position: absolute;
  /* bottom: 0; */
  top: 100%;
  left: 0;
  /* Changed from 'right' to 'left' */
  width: 100%;
  height: 100vh;
  background-color: rgb(0, 0, 0);
  display: none;
  transform-origin: bottom;
  clip-path: inset(0); /* Initially, clip the overlay to a square shape */
  transition: top 0.8s ease-in-out;
  /* clip-path: circle(0% at 22% 10%); */
  /* transition: all 0.8s ease-in-out; */
  /* transition: clip-path 0.9s ease-in-out; */
}

.overlay3.active {
  clip-path: circle(100% at center);
  top: 0;
  /* clip-path: circle(150% at 0% 0%); */
  /* Changed '100%' to '150%' to extend the clip path further */
}

.content3 {
  box-shadow: 0 0 20px 5px #48abe0;
  /* background-color: rgba(109, 109, 109, 0.845); */
  padding: 20px;
  height: 80vh;
  width: 80%;
  text-align: center;
  align-items: center;
  justify-content: center;
  margin: 10vh auto;
  display: none;
  animation: slideFromLeft 1.5s forwards ease; /* Add animation property */
  opacity: 0; /* Start with opacity set to 0 */
}
.content3 iframe {
  margin: auto;
}

.close-button3 {
  position: absolute;
  top: 10px;
  right: 10px;
  font-size: 35px;
  width: 50px;
  height: 50px;
  outline: none;
  border: none;
  color: white;
  border: 2px solid white;
  border-radius: 50%;
  cursor: pointer;
  background-color: transparent;
  transition: all 0.5s ease-in;
}
.close-button3:hover {
  background-color: black;
  transition: all 0.5s ease-in;
}
/* overlay 3 end */

/* overlay 4 Start */
.overlay4 {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  display: none;
  transform-origin: bottom right;
  clip-path: circle(0% at 80% 80%);
  transition: clip-path 0.8s ease-in-out;
}

.overlay4.active {
  clip-path: circle(150% at 0% 0%);
}

.content4 {
  background-color: rgba(0, 0, 0, 0.62);
  padding: 20px;
  height: 90vh;
  width: 80%;
  text-align: center;
  margin: 10vh auto;
}

.social {
  width: 100%;
  height: 50%;
  margin-top: 10%;
}

.social h1 {
  margin: 15px;
  letter-spacing: 3px;
  font-size: 40px;
  color: white;
  font-weight: 700;
  text-transform: uppercase;
  font-family: "Share Tech Mono", monospace;
}

.social-links {
  width: 80%;
  margin: 13% auto;
  display: flex;
  align-items: flex-start;

  justify-content: space-evenly;
}

.social-links img {
  margin: auto;
  /* border: 2px solid yellow; */
  display: flex;
  align-items: center;
  justify-content: center;
  transition: 0.5s ease-in;
  width: 120px;
  background-size: cover;
  height: 100%;
}

/* .social-links .insta{
     width: 115px;
   }
   .social-links .fb{
     width: 115px;
   } */
.social-links img:hover {
  transform: scale(1.1);
  transition: 0.5s ease-in;
}

.close-button4 {
  position: absolute;
  top: 11%;
  right: 10.5%;
  font-size: 35px;
  width: 50px;
  height: 50px;
  outline: none;
  border: none;
  color: white;
  border: 2px solid white;
  border-radius: 50%;
  cursor: pointer;
  background-color: transparent;
  transition: all 0.5s ease-in;
}

.close-button4:hover {
  background-color: black;
  transition: all 0.5s ease-in;
}
/* overlay 4 end */
/* Overlay End  */

/* Privacy Policy Page */
.pp-intro {
  color: #e4dde1;
  font-size: 1rem;
}

.pp-cluase {
  color: #e4dde1;
  padding-left: 14px;
  gap: 4;
  font-size: 1.1rem;
}

.pp-bullet {
  font-size: 20px;
  color: #ffffff;
  font-size: 1.2rem;
}

.pp-bg-layer {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.942);
  z-index: -3;
}

.pp-bg-logo {
  width: 85%; /* Set width to 75% of the parent/container */
  height: 100vh; /* Maintain the aspect ratio */
  position: fixed; /* Keeps the logo fixed in place */
  top: 50%; /* Center vertically */
  left: 50%; /* Center horizontally */
  transform: translate(-50%, -50%); /* Adjust to the exact center */
  opacity: 0.08; /* Set desired opacity */
  z-index: -1; /* Ensure it remains behind the content */
}

.pp-content {
  position: relative;
  z-index: 1;
  padding: 20px;
}

.footer-text {
  position: absolute; /* Makes the element positioned relative to its nearest positioned ancestor */
  bottom: 1rem; /* Positions it 1rem from the bottom */
  right: 1rem; /* Positions it 1rem from the right */
  color: #9ca3af; /* Equivalent to Tailwind's `text-gray-400` */
  background-color: transparent; /* Ensures the background is transparent */
  font-size: 0.75rem; /* Equivalent to Tailwind's `text-xs` */
}
