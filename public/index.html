<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="shortcut icon" href="images/logo.png" type="image/png">
    <title>Melodyze.ai</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/responsive.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        .main-icons {
            top: 0 !important
        }

        .main-icons .top-icon1,
        .main-icons .top-icon2,
        .main-icons1 .top-icon3,
        .main-icons1 .top-icon4 {
            width: 18% !important;
        }

        .main-icons1 .top-icon3,
        .main-icons .top-icon1 {
            margin-left: 13.7% !important;
        }

        .main-icons1 .top-icon4,
        .main-icons .top-icon2 {
            right: 13.7% !important;
        }


        @media only screen and (max-width:500px) {

            .main-icons .top-icon1,
            .main-icons .top-icon2,
            .main-icons1 .top-icon3,
            .main-icons1 .top-icon4 {
                width: 29% !important;
                height: 29% !important;
            }

            .main-icons1 .top-icon3,
            .main-icons .top-icon1 {
                margin-left: 14% !important;
            }

            .main-icons1 .top-icon4,
            .main-icons .top-icon2 {
                right: 14% !important;
            }

            .topIcons {
                margin-top: 83px !important
            }

            .bottomIcons {
                margin-bottom: 130px !important;
            }
        }
    </style>
    <script>
        function adjustTopIconsMargin() {
            var windowHeight = window.innerHeight;
            var windowWidth = window.innerWidth;
            if (windowWidth < 501) {
                // document.querySelectorAll('.topIcons').forEach(function(element) {
                //     element.style.marginTop = 45 + (windowHeight - 370) / 12 + 'px';
                // });
                // document.querySelectorAll('.bottomIcons').forEach(function(element) {
                //     element.style.marginBottom = 70 + (windowHeight - 370) / 8 + 'px';
                // });
                // document.querySelectorAll('.imageHeight').forEach(function(element) {
                //     element.style.height = 50 + (windowHeight - 370)* 0.20 + 'px';
                // });
            } else {
                document.querySelectorAll('.topIcons').forEach(function (element) {
                    element.style.marginTop = 21 + (windowHeight - 370) / 12 + 'px';
                    // 20 se 21 done
                });
                document.querySelectorAll('.bottomIcons').forEach(function (element) {
                    element.style.marginBottom = 20 + (windowHeight - 370) / 10 + 'px';
                });
                document.querySelectorAll('.imageHeight').forEach(function (element) {
                    element.style.height = 117 + (windowHeight - 370) * 0.27 + 'px';
                    // 115 se 117 done
                });
            }
        }

        document.addEventListener("DOMContentLoaded", adjustTopIconsMargin);
        window.addEventListener("resize", adjustTopIconsMargin);
    </script>
</head>

<body>
    <!-- <nav style="display: none;">
		<a href="/">Home</a>
		<a href="/privacy-policy">Privacy Policy</a>
	</nav> -->
    <div class="max-scrn">
        <!-- Intro Video Start -->
        <!-- Intro Video End -->
        <div class="bg-blck" id="bg-blck"></div>

        <div id="menudisplay">
            <!-- Loop Video Start -->

            <div class="intro-vdo">

                <video playsinline="playsinline" autoplay="autoplay" muted="muted" loop="loop" width="100%"
                    preload="auto" height="100%">
                    <source
                        src="https://cdn.jsdelivr.net/gh/animesh-melodyze/public-contents@main/Landscape_loop_icons.webm"
                        type="video/webm">
                    <source
                        src="https://cdn.jsdelivr.net/gh/animesh-melodyze/public-contents@main/Landscape_loop_icons.mp4"
                        type="video/mp4">
                </video>
            </div>
            <div class="intro-vdo1">

                <video playsinline="playsinline" autoplay="autoplay" muted="muted" loop="loop" width="100%"
                    preload="auto" height="100%">
                    <source
                        src="https://cdn.jsdelivr.net/gh/animesh-melodyze/public-contents@main/Portrait_loop_icons.webm"
                        type="video/webm">
                    <source
                        src="https://cdn.jsdelivr.net/gh/animesh-melodyze/public-contents@main/Portrait_loop_icons.mp4"
                        type="video/mp4">
                </video>
            </div>

            <!-- Loop Video End -->

            <!-- Four Icons Start -->
            <div class="main-icons">
                <div class="top-icon1 topIcons imageHeight open-button">
                    <!-- <img class="open-button" src="images/Retry_icon.png" alt=""> -->
                </div>
                <div class="top-icon2 topIcons imageHeight open-button2">
                    <!-- <img class="open-button2" src="images/Signup_icon.png" alt=""> -->
                </div>
            </div>
            <div class="main-icons1">
                <div class="top-icon3 bottomIcons imageHeight open-button3">
                    <!-- <img class="open-button3" src="images/Youtube_icon.png" alt="" srcset=""> -->

                </div>
                <div class="top-icon4 bottomIcons imageHeight open-button4">
                    <!-- <img class="open-button4" src="images/Social_icon.png" alt="" srcset=""> -->
                </div>
            </div>

            <!-- Four Icons End -->
        </div>
        <div class="overlay" id="overlay" style="display: none;">
            <div style="display: block;height: 100%;width: 100%;">
                <video class="vedo1" id="video" width="100%" preload="auto" loop autoplay muted height="100%">
                    <source
                        src="https://melodyze-public-bucket.s3.ap-south-1.amazonaws.com/website_videoes/promo_video_1080p.mp4">
                </video>

                <!-- <button class="close-button">&times;</button> -->
                <style>
                    .skip-button {
                        /* bottom: 30px;
                        left: 10%;
                        transform: translateX(-50%);
                        padding: 7px 35px;
                        border: none;
                        background: #0048c1;
                        color: #fff;
                        font-weight: normal;
                        z-index: 99999;
                        cursor: pointer;
                        border-radius: 20px; */

                        transform: translateX(-50%);
                        padding: 10px;
                        border: none;
                        background: #3725e847;
                        color: #fff;
                        z-index: 99999;
                        cursor: pointer;
                        border-radius: 50px;
                    }

                    .unmute-button {
                        /* margin-left: 10px;
                        bottom: 30px;
                        left: 20%;
                        transform: translateX(-50%);
                        padding: 7px 35px;
                        border: none;
                        background: #0048c1;
                        color: #fff;
                        font-weight: normal;
                        z-index: 99999;
                        cursor: pointer;
                        border-radius: 20px; */

                        transform: translateX(-50%);
                        padding: 10px;
                        border: none;
                        background: #3725e847;
                        color: #fff;
                        z-index: 99999;
                        cursor: pointer;
                        border-radius: 50px;
                    }
                </style>
                <div
                    style="position: absolute;right: 0px;top: 20px;display: flex;align-items: center;gap: 15px;flex-direction: column;">
                    <button class="skip-button">
                        <!-- <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="#fff" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <polygon points="5 4 15 12 5 20 5 4"></polygon>
                            <line x1="19" y1="5" x2="19" y2="19"></line>
                        </svg>
                        Skip -->
                        <svg xmlns="http://www.w3.org/2000/svg" width="30px" height="30px" viewBox="0 0 24 24"
                            fill="none">
                            <g fill="#FFFFFF">
                                <path xmlns="http://www.w3.org/2000/svg"
                                    d="M20.7457 3.32851C20.3552 2.93798 19.722 2.93798 19.3315 3.32851L12.0371 10.6229L4.74275 3.32851C4.35223 2.93798 3.71906 2.93798 3.32854 3.32851C2.93801 3.71903 2.93801 4.3522 3.32854 4.74272L10.6229 12.0371L3.32856 19.3314C2.93803 19.722 2.93803 20.3551 3.32856 20.7457C3.71908 21.1362 4.35225 21.1362 4.74277 20.7457L12.0371 13.4513L19.3315 20.7457C19.722 21.1362 20.3552 21.1362 20.7457 20.7457C21.1362 20.3551 21.1362 19.722 20.7457 19.3315L13.4513 12.0371L20.7457 4.74272C21.1362 4.3522 21.1362 3.71903 20.7457 3.32851Z" />
                            </g>
                        </svg>
                    </button>
                    <button class="unmute-button" id="intro-unmute" style="display: none;">
                        <!-- <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="#fff" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                <polygon points="11 5 6 9 2 9 2 15 6 15 11 19 11 5"></polygon>
                                                <path d="M15.54 8.46a5 5 0 0 1 0 7.07"></path>
                                                <path d="M19.07 4.93a10 10 0 0 1 0 14.14"></path>
                                            </svg>
                                            Unmute Audio -->
                        <svg xmlns="http://www.w3.org/2000/svg" fill="#000000" width="30px" height="30px"
                            viewBox="0 0 24 24">
                            <g fill="#FFFFFF">
                                <path fill-rule="evenodd"
                                    d="M11.553 3.064A.75.75 0 0112 3.75v16.5a.75.75 0 01-1.255.555L5.46 16H2.75A1.75 1.75 0 011 14.25v-4.5C1 8.784 1.784 8 2.75 8h2.71l5.285-4.805a.75.75 0 01.808-.13zM10.5 5.445l-4.245 3.86a.75.75 0 01-.505.195h-3a.25.25 0 00-.25.25v4.5c0 .138.112.25.25.25h3a.75.75 0 01.505.195l4.245 3.86V5.445z" />
                                <path
                                    d="M18.718 4.222a.75.75 0 011.06 0c4.296 4.296 4.296 11.26 0 15.556a.75.75 0 01-1.06-1.06 9.5 9.5 0 000-13.436.75.75 0 010-1.06z" />
                                <path
                                    d="M16.243 7.757a.75.75 0 10-1.061 1.061 4.5 4.5 0 010 6.364.75.75 0 001.06 1.06 6 6 0 000-8.485z" />
                            </g>
                        </svg>
                    </button>
                    <button class="unmute-button" id="intro-mute" style="display: none;">
                        <!-- <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="#fff" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                <polygon points="11 5 6 9 2 9 2 15 6 15 11 19 11 5"></polygon>
                                                <path d="M15.54 8.46a5 5 0 0 1 0 7.07"></path>
                                                <path d="M19.07 4.93a10 10 0 0 1 0 14.14"></path>
                                            </svg>
                                            Unmute Audio -->
                        <svg xmlns="http://www.w3.org/2000/svg" fill="#000000" width="30px" height="30px"
                            viewBox="0 0 24 24">
                            <g fill="#FFFFFF">
                                <path fill-rule="evenodd"
                                    d="M12 3.75a.75.75 0 00-1.255-.555L5.46 8H2.75A1.75 1.75 0 001 9.75v4.5c0 .966.784 1.75 1.75 1.75h2.71l5.285 4.805A.75.75 0 0012 20.25V3.75zM6.255 9.305l4.245-3.86v13.11l-4.245-3.86a.75.75 0 00-.505-.195h-3a.25.25 0 01-.25-.25v-4.5a.25.25 0 01.25-.25h3a.75.75 0 00.505-.195z" />
                                <path
                                    d="M16.28 8.22a.75.75 0 10-1.06 1.06L17.94 12l-2.72 2.72a.75.75 0 101.06 1.06L19 13.06l2.72 2.72a.75.75 0 101.06-1.06L20.06 12l2.72-2.72a.75.75 0 00-1.06-1.06L19 10.94l-2.72-2.72z" />
                            </g>
                        </svg>
                    </button>
                </div>
            </div>
        </div>
        <!-- Overlay 1 Start -->
        <div class="overlay overlay-1" id="overlay">
            <div class="l-logo"> <img class="l-logo2" src="images/Melodyze new jacket logo.png" alt=""></div>
            <div class="content">
                <video class="vedo1" id="videoReset" width="100%" preload="auto" loop autoplay muted height="100%"
                    controls>
                    <source
                        src="https://melodyze-public-bucket.s3.ap-south-1.amazonaws.com/website_videoes/promo_video_1080p.mp4">
                </video>
                <button class="close-button">&times;</button>
            </div>
        </div>
        <!-- Overlay 1 End -->

        <!-- Overlay 2 Start -->
        <div class="overlay2" id="overlay2">
            <div class="content2">
                <div class="signup-r-side">
                    <form id="myForm" action="" method="post">
                        <h1>Sign up for early access</h1>
                        <div class="frm-names">
                            <div class="Uname">
                                <label for="fname">Name <span>*</span></label> <br>
                                <input type="text" name="fname" id="fname" required>
                            </div>
                        </div>
                        <div class="frm-names">
                            <div class="fname">
                                <label for="email">Email <span>*</span></label> <br>
                                <input type="email" name="email" id="email" required>
                            </div>
                            <div class="lname">
                                <label for="phone">Phone</label> <br>
                                <input type="tel" name="phone" id="phone" maxlength="14">
                            </div>
                        </div>
                        <br>
                        <div class="frm-names">
                            <div class="find-us">
                                <label for="find">
                                    Share your social media link (YouTube, Facebook, IG, TikTok,
                                    Soundcloud, Threads, etc.)
                                </label> <br>
                                <input type="url" name="socialMediaLink" id="socialMediaLink">
                            </div>
                        </div>
                        <br />
                        <div class="frm-names">
                            <div class="find-us">
                                <label for="musichelp">How can we support you on your musical journey ?
                                </label> <br>
                                <input type="text" name="musichelp" id="musichelp">
                            </div>
                        </div>
                        <br>
                        <div class="frm-names">
                            <div class="find-us">
                                <label for="find">How did you find us ?</label> <br>
                                <input type="text" name="findUs" id="findUs">
                            </div>
                        </div>
                        <br>


                        <!-- <div class="share-file">
                            <h3>Share your singing Skills with us</label></h3>
                            <div class="frm-names">
                                <div class="fname">
                                    <label for="shareSong1">
                                        Paste a URL
                                    </label> <br/>
                                    <input type="url" name="shareSong1" id="shareSong1" placeholder="">
                                </div>
                                <br>
                                <span class="more-space">OR</span>
                                <br>
                                <div class="lname">
                                    <label for="shareSong1">Upload a Song</label> <br>
                                    <input type="file" name="shareSong1" id="audio-upload1" accept="audio/*">
                                </div>
                            </div>
                            <div id="message"></div>
                        </div> -->


                        <button class="btnsub" type="submit">Sign Me Up</button>
                    </form>
                </div>
                <button class="close-button2">&times;</button>
                <div id="loadingDiv" style="display: none;">
                    <div class="spinner"></div>
                </div>
                <div id="messageDiv" style="display: none;">
                    <h1><span>Congratulations...! </span><br> sign up successful</h1>
                </div>
            </div>
        </div>
        <!-- Overlay 2 End -->

        <!-- Overlay 3 Start -->

        <div class="overlay3">
            <div class="l-logo"><img class="l-logo1" src="images/Melodyze new jacket logo.png" alt="">
            </div>
            <div class="content3">
                <iframe class="frame3" id="player" width="80%" height="80%"
                    src="https://www.youtube.com/embed/iLMD5-GllC4?si=vsyyldE_Qycmmsx0" title="YouTube video player"
                    frameborder="0"
                    allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
                    allowfullscreen></iframe>
                <button class="close-button3">&times;</button>
            </div>
        </div>
        <!-- Overlay 3 End -->

        <!-- Overlay 4 Start -->
        <div class="overlay4">
            <div class="content4">
                <div class="social">
                    <h1>Connect With Us</h1>
                    <div class="social-links">
                        <a href="https://www.facebook.com/profile.php?id=61573154855877" target="_blank"><img
                                class="fb" src="images/facebook2.png" alt="" srcset=""></a>
                        <a href="https://www.instagram.com/melodyze_ai" target="_blank"><img
                                src="images/Instagram7.png" class="insta" alt="" srcset=""></a>

                        <a href="https://www.tiktok.com/@melodyze.ai" target="_blank"><img
                                src="images/1655891149tiktok-logo-png-images1.png" alt="" srcset=""></a>
                        <a href="https://www.youtube.com/@melodyze969" target="_blank"><img
                                src="images/Youtube_logo_PNG3.png" alt="" srcset=""></a>
                    </div>
                </div>
                <button class="close-button4">&times;</button>
            </div>
        </div>
        <!-- Overlay 4 End -->
    </div>
    <script src="javascripts/main.js?v=1"></script>
</body>

</html>