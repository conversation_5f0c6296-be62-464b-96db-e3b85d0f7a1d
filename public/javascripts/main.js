// Intro video JS

// Skip Button Overlay

// Overlay Function

const openButton = document.querySelector(".open-button");
const closeButton = document.querySelector(".close-button");
// const unmuteButton = document.querySelector(".unmute-button");
const introUnmuteButton = document.querySelector("#intro-unmute");
const introMuteButton = document.querySelector("#intro-mute"); ''
const skipButton = document.querySelector(".skip-button");
const overlay = document.querySelector(".overlay");
const vedo1 = document.querySelector(".vedo1");
const content1 = document.querySelector(".content");
const logo1 = document.querySelector(".l-logo2");

// Function to play video with error handling
async function playVideo() {
	try {
		await vedo1.play();
		console.log("Playback started successfully");
	} catch (error) {
		console.error("Playback error:", error);
	}
}

// Overlay One JS Start
openButton.addEventListener("click", function () {
	overlay.style.display = "block";
	vedo1.style.display = "block";

	setTimeout(function () {
		overlay.classList.add("active");
		overlay.style.opacity = "1";
	}, 500);

	setTimeout(function () {
		// vedo1.muted = false;
		content1.style.display = "flex";
	}, 1200);

	setTimeout(function () {
		playVideo();
	}, 3000);
});

logo1.addEventListener("click", function () {
	overlay.classList.remove("active");
	// vedo1.muted = true;
	vedo1.pause();

	setTimeout(function () {
		overlay.style.display = "none";
		vedo1.style.display = "none";
		overlay.style.opacity = "0";
		content1.style.display = "none";
	}, 1000);
});

closeButton.addEventListener("click", function () {
	overlay.classList.remove("active");
	// vedo1.muted = true;
	vedo1.pause();

	setTimeout(function () {
		overlay.style.display = "none";
		vedo1.style.display = "none";
		overlay.style.opacity = "0";
		content1.style.display = "none";
	}, 1000);
});

introMuteButton.addEventListener("click", function () {
	vedo1.muted = true;
	introMuteButton.style.display = "none";
	introUnmuteButton.style.display = "block"
	// unmuteButton.remove();
});

introUnmuteButton.addEventListener("click", function () {
	vedo1.muted = false;
	introUnmuteButton.style.display = "none";
	introMuteButton.style.display = "block"
	// unmuteButton.remove();
});

skipButton.addEventListener("click", function () {
	overlay.classList.remove("active");
	// vedo1.muted = true;
	vedo1.pause();

	setTimeout(function () {
		overlay.style.display = "none";
		vedo1.style.display = "none";
		overlay.style.opacity = "0";
		content1.style.display = "none";
	}, 1000);

	// resetFormAndMessage();
	// overlay2.style.display = "block";
	// overlay2.style.clipPath = "circle(0% at 80% 10%)";
	// overlay2.style.backgroundColor = "rgba(0, 0, 0, 0.5)";
	// setTimeout(function () {
	// 	overlay2.classList.add("active");
	// 	overlay2.style.opacity = "1";
	// 	overlay2.style.clipPath = "circle(150% at 0% 0%)";
	// }, 200);
});

// Overlay One JS End

// Overlay Two JS Start
// Function to handle showing overlay2

// Initial display of overlay2 on page load
document.addEventListener("DOMContentLoaded", function () {
	// Selecting elements
	const overlay2 = document.querySelector(".overlay2");
	const openButton2 = document.querySelector(".open-button2");
	const closeButton2 = document.querySelector(".close-button2");
	const menudisplay = document.getElementById("menudisplay");

	// Initial display of overlay2 without clip-path effect
	overlay2.style.display = "none";
	overlay2.style.opacity = "1"; // Ensure opacity is set to 1 for initial display
	overlay2.style.clipPath = "none"; // Remove clip-path initially
	overlay2.style.backgroundColor = "rgba(0, 0, 0, 0.5)"; // Adjust background color if needed
	menudisplay.style.display = "block"; // Show menudisplay if hidden during opening

	// Event listener for opening overlay2
	// Event listener for closing overlay2
	closeButton2.addEventListener("click", function () {
		setTimeout(function () {
			// overlay2.classList.remove('active'); // Remove active class for clip-path effect
			// overlay2.style.display = 'none'; // Hide overlay2
			// overlay2.style.opacity = '0'; // Ensure opacity is set appropriately
			menudisplay.style.display = "block"; // Show menudisplay if hidden during opening
		}, 0);
	});

	if (vedo1.muted) {
		introMuteButton.style.display = "none";
		introUnmuteButton.style.display = "block";
	} else {
		introMuteButton.style.display = "block";
		introUnmuteButton.style.display = "none"
	}
});

// Overlay Two JS End

// Overlay Three JS Start
const overlay3 = document.querySelector(".overlay3");
const openButton3 = document.querySelector(".open-button3");
const closeButton3 = document.querySelector(".close-button3");
const frame3 = document.querySelector(".frame3");
const content3 = document.querySelector(".content3");
const logo3 = document.querySelector(".l-logo1");

openButton3.addEventListener("click", function () {
	overlay3.style.display = "block";
	frame3.style.display = "block";
	setTimeout(function () {
		overlay3.classList.add("active");
		overlay3.style.opacity = "1";
	}, 500);
	setTimeout(function () {
		content3.style.display = "flex";
	}, 1200);
});
logo3.addEventListener("click", function () {
	overlay3.classList.remove("active");
	// frame3.target.mute();
	setTimeout(function () {
		content3.style.display = "none";
		frame3.style.display = "none";
		overlay3.style.display = "none";
		overlay3.style.opacity = "0";
	}, 1000);
});

closeButton3.addEventListener("click", function () {
	overlay3.classList.remove("active");
	// frame3.target.mute();
	setTimeout(function () {
		content3.style.display = "none";
		frame3.style.display = "none";
		overlay3.style.display = "none";
		overlay3.style.opacity = "0";
	}, 1000);
});

// Overlay3 audio js when overlay closed audio also off

// Pause the YouTube video and hide the container
function pauseVideoAndHideContainer() {
	const player = document.getElementById("player");
	const videoSrc = player.getAttribute("src");

	// Pause the video by modifying the video source
	player.setAttribute("src", "");
	player.setAttribute("src", videoSrc);
}

// Event listener for the close button
closeButton3.addEventListener("click", pauseVideoAndHideContainer);
logo3.addEventListener("click", pauseVideoAndHideContainer);

// Overlay Three JS End

// Overlay Four JS Start
const closeButton4 = document.querySelector(".close-button4");
const overlay4 = document.querySelector(".overlay4");
const openButton4 = document.querySelector(".open-button4");

openButton4.addEventListener("click", function () {
	overlay4.style.display = "block";
	setTimeout(function () {
		overlay4.classList.add("active");
		overlay4.style.opacity = "1";
	}, 200);
});

closeButton4.addEventListener("click", function () {
	overlay4.classList.remove("active");
	setTimeout(function () {
		overlay4.style.display = "none";
		overlay4.style.opacity = "0";
	}, 1000);
});

// Overlay Four JS End

// Form Submission and input and file  JS Start
// function validateAudioUpload(inputId) {
// 	const audioInput = document.getElementById(inputId);
// 	const file = audioInput.files[0];
// 	const validExtensions = ["audio/mpeg", "audio/wav", "audio/mp3"]; // Add more valid MIME types if needed

// 	if (file && validExtensions.includes(file.type)) {
// 		console.log("Valid audio file");
// 	} else {
// 		alert("Please upload a valid audio file (mp3 or wav).");
// 		audioInput.value = ""; // Clear the invalid file
// 	}
// }

// document
// 	.getElementById("audio-upload1")
// 	.addEventListener("change", function () {
// 		validateAudioUpload("audio-upload1");
// 	});

// const shareLinkInput = document.getElementById("shareSong1");
// const audioUploadInput = document.getElementById("audio-upload1");
// const messageDiv1 = document.getElementById("message");

// function toggleInputs() {
// 	if (shareLinkInput.value.trim() !== "") {
// 		audioUploadInput.disabled = true;
// 		messageDiv1.textContent = "";
// 	} else {
// 		audioUploadInput.disabled = false;
// 	}

// 	if (audioUploadInput.files.length > 0) {
// 		shareLinkInput.disabled = true;
// 		messageDiv1.textContent = "";
// 	} else {
// 		shareLinkInput.disabled = false;
// 	}
// }

// function handleDisabledClick(event) {
// 	if (shareLinkInput.disabled || audioUploadInput.disabled) {
// 		if (
// 			event.target === audioUploadInput &&
// 			audioUploadInput.files.length > 0
// 		) {
// 			return;
// 		}
// 		alert("You can only fill one field.");
// 		event.preventDefault();
// 	}
// }

// shareLinkInput.addEventListener("input", toggleInputs);
// audioUploadInput.addEventListener("change", toggleInputs);

// shareLinkInput.addEventListener("click", handleDisabledClick);
// audioUploadInput.addEventListener("click", handleDisabledClick);

const myForm = document.getElementById("myForm");
const messageDiv = document.getElementById("messageDiv");
const overlay2 = document.getElementById("overlay2");
const menudisplay = document.getElementById("menudisplay");
const loadingDiv = document.getElementById("loadingDiv");

// Function to clear all form fields
function clearFormFields() {
	myForm.reset();
	// shareLinkInput.disabled = false;
	// audioUploadInput.disabled = false;
}

// Reset form and message div state when overlay2 is opened
function resetFormAndMessage() {
	myForm.style.display = "block";
	messageDiv.style.display = "none";
	clearFormFields();
}


// Event listener for opening overlay2
document.addEventListener("DOMContentLoaded", function () {
	var video = document.getElementById("video");
	video.play();

	const openButton2 = document.querySelector(".open-button2");
	const closeButton2 = document.querySelector(".close-button2");

	overlay2.style.display = "none";
	overlay2.style.opacity = "1";
	overlay2.style.clipPath = "none";
	overlay2.style.backgroundColor = "rgba(0, 0, 0, 0.5)";
	menudisplay.style.display = "block";

	openButton2.addEventListener("click", function () {
		resetFormAndMessage();
		overlay2.style.display = "block";
		overlay2.style.clipPath = "circle(0% at 80% 10%)";
		overlay2.style.backgroundColor = "rgba(0, 0, 0, 0.5)";
		setTimeout(function () {
			overlay2.classList.add("active");
			overlay2.style.opacity = "1";
			overlay2.style.clipPath = "circle(150% at 0% 0%)";
		}, 200);
	});

	resetFormAndMessage();
	overlay2.style.display = "block";
	overlay2.style.clipPath = "circle(0% at 80% 10%)";
	overlay2.style.backgroundColor = "rgba(0, 0, 0, 0.5)";
	setTimeout(function () {
		overlay2.classList.add("active");
		overlay2.style.opacity = "1";
		overlay2.style.clipPath = "circle(150% at 0% 0%)";
	}, 200);
});

const openButton21 = document.querySelector(".open-button2");
const closeButton21 = document.querySelector(".close-button2");
openButton21.addEventListener("click", function () {
	resetFormAndMessage();
	overlay2.style.display = "block";
	overlay2.style.clipPath = "circle(0% at 80% 10%)";
	overlay2.style.backgroundColor = "rgba(0, 0, 0, 0.5)";
	setTimeout(function () {
		overlay2.classList.add("active");
		overlay2.style.opacity = "1";
		overlay2.style.clipPath = "circle(150% at 0% 0%)";
	}, 200);
});

closeButton21.addEventListener("click", function () {
	overlay2.classList.remove("active");
	overlay2.style.clipPath = "circle(0% at 80% 10%)";
	setTimeout(function () {
		overlay2.style.clipPath = "circle(150% at 0% 0%)";
		overlay2.style.display = "none";
		overlay2.style.opacity = "0";
	}, 1000);
});



// Form submission event listener

myForm.addEventListener("submit", async function (e) {
	e.preventDefault();
	loadingDiv.style.display = "flex";

	const formData = new FormData(myForm).entries();
	const fields = Object.fromEntries(formData);
	console.log(fields);

	// let song_upload_data = {};
	// let uploaded_audio = undefined;
	// let put_signed_url = undefined;

	// if (typeof fields.shareSong1 === "string") {
	// 	song_upload_data = { sharedAudioUrl: fields.shareSong1 };
	// } else {
	// 	const formData = new FormData(myForm).entries();
	// 	const files = Object.fromEntries(formData);

	// 	uploaded_audio = files.shareSong1
	// 	console.log(uploaded_audio);
	// 	const fileExt = uploaded_audio.name?.split(".")?.reverse()?.[0] || "mp3";

	// 	const signed_url_response = await fetch(
	// 		`https://app-api.melodyze.ai/web/v1/signup/audio_upload_signed_url?ext=${fileExt}&mimetype=${uploaded_audio.type}`,
	// 		{ method: "GET" }
	// 	);

	// 	const url_json = await signed_url_response.json();
	// 	console.log("get_signed_url_response:", url_json);

	// 	put_signed_url = url_json?.data?.signed_url;
	// 	song_upload_data = { uploadedAudioPath: url_json?.data?.path };

	// }

	try {
		const formData2 = new FormData(myForm);
		console.log("submitting form");
		const response = await fetch(
			"https://app-api.melodyze.ai/web/v1/signup/submit_form",
			{
				method: "POST",
				headers: { "Content-Type": "application/json" },
				body: JSON.stringify({
					...Object.fromEntries(formData2),
					// ...song_upload_data,
				}),
			}
		);

		const result = await response.json();
		console.log(result);
		if (result.success === true) {

			// if (put_signed_url) {
			// 	await fetch(put_signed_url, {
			// 		method: "PUT",
			// 		body: uploaded_audio,
			// 		headers: {
			// 			"Content-Type": uploaded_audio.type,
			// 		},
			// 	});
			// }

			clearFormFields();

			loadingDiv.style.display = "none";
			myForm.style.display = "none";
			messageDiv.style.display = "flex";

			setTimeout(function () {
				overlay2.style.display = "none";
				menudisplay.style.display = "block";
			}, 5000);

		} else {
			loadingDiv.style.display = "none";
			console.error("Failed to submit form:", result.error);
			window.alert(result.error);
		}

	} catch (error) {
		loadingDiv.style.display = "none";
		console.error("Exception:", error);
		window.alert("Failed to submit form. Please try again later.");
	}
});
