Firebase sites:

"site": "melodyze-website-test",
"site": "melodyze-ai",

## Blog Update Scripts

We have provided scripts to automate the process of updating blog content. These scripts clear the existing blog directory, extract new content from a provided ZIP file, and push changes to Git.

### For Unix-based systems (Linux/macOS):

#### `push-changes.sh`

This script is for Unix-based systems and automates the blog update process.

#### Usage:
```sh
./push-changes.sh path/to/blog-content.zip
```

### For Windows:

#### `push-changes.bat`

This script is for Windows and automates the blog update process.

#### Usage:
```bat
push-changes.bat path/to/blog-content.zip
```

Make sure to provide the path to the ZIP file as an argument to the script.