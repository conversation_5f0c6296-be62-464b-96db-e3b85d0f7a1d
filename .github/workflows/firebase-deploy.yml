name: Deploy to Firebase Hosting

on:
  push:
    branches:
      - main

jobs:
  deploy:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '20'

      - name: Install Firebase CLI
        run: npm install -g firebase-tools

      - name: Deploy to Firebase
        run: firebase deploy --project melodyze-65923 --only hosting:melodyze-ai --token "1//0gauK8RTRmo_JCgYIARAAGBASNwF-L9IrTrixZMN4s5Sr6n4VJTlddAGSsEZAqRWTxIh49D53HwaFZiOJge4N2SJiGzDaFkzZRZU"
        # env:
        #   FIREBASE_TOKEN: ${{ secrets.FIREBASE_TOKEN }}
