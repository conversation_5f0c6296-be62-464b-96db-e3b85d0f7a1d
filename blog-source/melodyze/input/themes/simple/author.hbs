{{> head}}
{{> navbar}}
<main class="page page--author">
   {{#author}}
      <div class="hero {{#checkIfAll authorViewConfig.displayFeaturedImage featuredImage.url}}
   {{else}}hero--noimage{{/checkIfAll}}">
         <header class="hero__content {{#checkIf @config.custom.alignHero '==' "center" }}hero__content--centered{{/checkIf}}">
            <div class="wrapper {{#checkIf @config.custom.alignHero '!=' "center" }}page--author__wrapper{{/checkIf}}">
                  {{#if authorViewConfig.displayAvatar}}        
                     {{#if avatar}}
                        <img 
                           src="{{avatarImage.url}}" 
                           {{ lazyload "lazy" }}
                           height="{{avatarImage.height}}"
                           width="{{avatarImage.width}}" 
                           class="page--author__avatar" 
                           alt="{{avatarImage.alt}}">
                     {{/if}}
                  {{/if}}
                  <div>
                     <h1>
                        {{name}}
                        {{#if authorViewConfig.displayPostCounter}}<sup>({{postsNumber}})</sup>{{/if}}
                     </h1>
                     {{#if authorViewConfig.displayDescription}} 
                        {{#if description}}
                           <div class="page__desc">{{{description}}}</div>
                        {{/if}}  
                     {{/if}}
                     {{#if authorViewConfig.displayWebsite}}  
                        {{#if website}}
                           <div class="page--author__website">
                              <a href="{{website}}" target="_blank" rel="nofollow noreferrer noopener" class="btn btn--icon">
                                 <span>{{ translate 'author.visitWebsite' }}</span>
                                 <svg height="18" width="18" aria-hidden="true">
                                    <use xlink:href="{{@website.assetsUrl}}/svg/svg-map.svg#website"/>
                                 </svg>
                              </a>
                           </div>
                        {{/if}}  
                     {{/if}} 
                  </div>
            </div>
         </header>

         {{#if authorViewConfig.displayFeaturedImage}}
            {{#featuredImage}}
               {{#if url}}
                  <figure class="hero__image">   
                     <div class="hero__image-wrapper">                   
                        <img
                           src="{{url}}"
                           {{#if @config.site.responsiveImages}}                           
                                 {{responsiveImageAttributes 'authorImage' srcset sizes}}
                           {{/if}}
                           {{ lazyload "eager" }}
                           height="{{height}}"
                           width="{{width}}"
                           alt="{{alt}}">
                        </div>
                        
                     {{#checkIfAny caption credits}}
                        <figcaption>
                           {{caption}}
                           {{credits}}
                        </figcaption>                      
                     {{/checkIfAny}}                      
                  </figure>
               {{/if}}
            {{/featuredImage}}
         {{/if}}
      </div>

      {{#if authorViewConfig.displayPostList}} 
         <div class="wrapper feed">
            {{#each ../posts}}
            <article class="feed__item {{#checkIf @config.custom.alignFeed '==' "center" }}feed__item--centered{{/checkIf}}">
               {{#if @config.custom.feedFeaturedImage}}
                  {{#featuredImage}}
                     {{#if url}}
                        <figure class="feed__image">
                           <img
                              src="{{url}}"
                              {{#if @config.site.responsiveImages}}
                                 {{responsiveImageAttributes 'featuredImage' srcset.feed sizes.feed}}
                              {{/if}}
                              {{ lazyload "lazy" }}
                              height="{{height}}"
                              width="{{width}}"
                              alt="{{alt}}">
                        </figure>
                     {{/if}}
                  {{/featuredImage}}
               {{/if}}
               <div class="feed__content">
                  <header>
                     {{#checkIfAny @config.custom.feedAvatar @config.custom.feedAuthor @config.custom.feedDate}}
                        <div class="feed__meta">
                           {{#author}}
                              {{#if @config.custom.feedAvatar}}
                                 {{#if avatar}}
                                    {{#if @config.custom.feedAuthor}}
                                       <img
                                          src="{{avatarImage.url}}" 
                                          {{ lazyload "lazy" }}
                                          height="{{avatarImage.height}}"
                                          width="{{avatarImage.width}}"      
                                          class="feed__author-thumb"
                                          alt="">
                                    {{else}}
                                       <a href="{{url}}" class="feed__author-link">
                                          <img
                                             src="{{avatarImage.url}}" 
                                             {{ lazyload "lazy" }}
                                             height="{{avatarImage.height}}"
                                             width="{{avatarImage.width}}"      
                                             class="feed__author-thumb"
                                             alt="{{avatarImage.alt}}">
                                       </a>
                                    {{/if}}
                                 {{/if}}
                              {{/if}}
                              {{#if @config.custom.feedAuthor}}
                                 <a href="{{url}}" class="feed__author">{{name}}</a>
                              {{/if}}
                           {{/author}}
                           {{#if @config.custom.feedDate}}
                              {{#checkIf @config.custom.feedDateType '==' "published" }}
                                 <time datetime="{{date createdAt 'YYYY-MM-DDTHH:mm'}}" class="feed__date">
                                    {{#checkIf @config.custom.formatDate '!=' 'custom'}}
                                       {{date createdAt @config.custom.formatDate}}
                                    {{else}}
                                       {{date createdAt @config.custom.formatDateCustom}}
                                    {{/checkIf}}
                                 </time>
                              {{/checkIf}}
                              {{#checkIf @config.custom.feedDateType '==' "modified" }}
                                 <time datetime="{{date modifiedAt 'YYYY-MM-DDTHH:mm'}}" class="feed__date">
                                    {{#checkIf @config.custom.formatDate '!=' 'custom'}}
                                       {{date modifiedAt @config.custom.formatDate}}
                                    {{else}}
                                       {{date modifiedAt @config.custom.formatDateCustom}}
                                    {{/checkIf}}
                                 </time>
                              {{/checkIf}}
                           {{/if}}
                        </div>
                     {{/checkIfAny}}
                     <h2 class="feed__title">
                        <a href="{{url}}">
                           {{title}}
                        </a>
                     </h2>
                  </header>               
                  {{#if hasCustomExcerpt}}
                     {{{ excerpt }}}
                  {{else}}
                     <p>{{{ excerpt }}}</p>
                  {{/if}}
                  {{#if @config.custom.feedtReadMore}}
                     <a href="{{url}}" class="readmore feed__readmore">
                        {{ translate 'post.readMore' }}</a>
                  {{/if}}
               </div>
            </article>
            {{/each}}
            {{> pagination}}
         </div>
      {{/if}} 
   {{/author}}
</main>
{{> footer}}
