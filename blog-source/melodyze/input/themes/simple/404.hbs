{{> head}}
{{> navbar}}
<main class="page page--error">
  <div class="content">
      <div class="hero">
         <header class="hero__content {{#checkIf @config.custom.alignHero '==' "center" }}hero__content--centered{{/checkIf}}">
            <div class="wrapper">
               <h1>{{ translate 'error.404' }}</h1>

               <div class="page__desc">
                  <p>{{ translate 'error.message' }}</p>
               </div>
               
               <a href="{{@website.url}}" class="btn hero__cta btn--icon">
                  <svg width="18" height="18" aria-hidden="true">
                     <use xlink:href="{{@website.assetsUrl}}/svg/svg-map.svg#arrow-prev"/>
                  </svg>
                  <span>{{ translate 'error.button' }}</span>
               </a>
            </div>
         </header>
      </div>
   </div>
</main>
{{> footer}}
