<footer class="footer {{#if @config.post.displayRelatedPosts}}{{#checkIf @config.custom.relatedPostsNumber '>' "0"}}
footer--glued{{/checkIf}}{{/if}}">
    <div class="wrapper">
         {{#if menus.footerMenu}}
            <nav class="footer__nav">
                {{> simple-menu menus.footerMenu}}
            </nav>
        {{/if}}
        
        {{#if @config.custom.copyrightText}}
            <div class="footer__copyright">
                {{{@config.custom.copyrightText}}}
            </div>
        {{/if}}

        {{#if @config.custom.socialButtons}}
            <div class="footer__social">
                {{#if @config.custom.socialFacebook}}
                    <a href="{{@config.custom.socialFacebook}}" aria-label="{{ translate 'partials.footer.followFacebook' }}">
                        <svg>
                            <use xlink:href="{{@website.assetsUrl}}/svg/svg-map.svg#facebook"/>
                        </svg>
                    </a>
                {{/if}}
                {{#if @config.custom.socialTwitter}}
                    <a href="{{@config.custom.socialTwitter}}" aria-label="{{ translate 'partials.footer.followX' }}">
                        <svg>
                            <use xlink:href="{{@website.assetsUrl}}/svg/svg-map.svg#twitter"/>
                        </svg>
                    </a>
                {{/if}}
                {{#if @config.custom.socialInstagram}}
                    <a href="{{@config.custom.socialInstagram}}" aria-label="{{ translate 'partials.footer.followInstagram' }}">
                        <svg>
                            <use xlink:href="{{@website.assetsUrl}}/svg/svg-map.svg#instagram"/>
                        </svg>
                    </a>
                {{/if}}
                {{#if @config.custom.socialLinkedin}}
                    <a href="{{@config.custom.socialLinkedin}}" aria-label="{{ translate 'partials.footer.followLinkedIn' }}">
                        <svg>
                            <use xlink:href="{{@website.assetsUrl}}/svg/svg-map.svg#linkedin"/>
                        </svg>
                    </a>
                {{/if}}
                {{#if @config.custom.socialVimeo}}
                    <a href="{{@config.custom.socialVimeo}}" aria-label="{{ translate 'partials.footer.followVimeo' }}">
                        <svg>
                            <use xlink:href="{{@website.assetsUrl}}/svg/svg-map.svg#vimeo"/>
                        </svg>
                    </a>
                {{/if}}
                {{#if @config.custom.socialPinterest}}
                    <a href="{{@config.custom.socialPinterest}}" aria-label="{{ translate 'partials.footer.followPinterest' }}">
                        <svg>
                            <use xlink:href="{{@website.assetsUrl}}/svg/svg-map.svg#pinterest"/>
                        </svg>
                    </a>
                {{/if}}
                {{#if @config.custom.socialYoutube}}
                    <a href="{{@config.custom.socialYoutube}}" aria-label="{{ translate 'partials.footer.followYoutube' }}">
                        <svg>
                            <use xlink:href="{{@website.assetsUrl}}/svg/svg-map.svg#youtube"/>
                        </svg>
                    </a>
                {{/if}}
            </div>
        {{/if}}
        
        {{#if @config.custom.backToTopButton}}
            <button onclick="backToTopFunction()" id="backToTop" class="footer__bttop" aria-label="{{ translate 'partials.footer.backToTop' }}" title="{{ translate 'partials.footer.backToTop' }}">
                <svg width="20" height="20">
                    <use xlink:href="{{@website.assetsUrl}}/svg/svg-map.svg#toparrow"/>
                </svg>
            </button>
        {{/if}}
    </div>
</footer>

<script defer src="{{js "scripts.min.js"}}"></script>

<script>window.publiiThemeMenuConfig={mobileMenuMode:'{{@config.custom.mobilemenu}}',animationSpeed:300,submenuWidth: {{#checkIf @config.custom.submenu '==' "auto" }}'auto'{{else}}{{@config.custom.submenuWidth}}{{/checkIf}},doubleClickTime:500,mobileMenuExpandableSubmenus:{{@config.custom.mobilemenuExpandableSubmenus}},relatedContainerForOverlayMenuSelector:'.top'};</script>

{{#if @config.site.mediaLazyLoad}}
    <script>        
        var images = document.querySelectorAll('img[loading]');
        for (var i = 0; i < images.length; i++) {
            if (images[i].complete) {
                images[i].classList.add('is-loaded');
            } else {
                images[i].addEventListener('load', function () {
                    this.classList.add('is-loaded');
                }, false);
            }
        }
    </script>
{{/if}}

{{#if @renderer.previewMode}}
<script defer src="{{js "svg-map.js"}}"></script>
<script defer src="{{js "svg-fix.js"}}"></script>
{{/if}}
{{{@footerCustomCode}}}
{{{ publiiFooter }}}
</body>
</html>
