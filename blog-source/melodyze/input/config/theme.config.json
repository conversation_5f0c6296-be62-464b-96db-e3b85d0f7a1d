{"config": {"postsPerPage": 5, "tagsPostsPerPage": 5, "authorsPostsPerPage": 5, "excerptLength": 45, "logo": "media/website/Melodyze-new-jacket-3.svg"}, "customConfig": {"pageMargin": "6vw", "pageWidth": "66rem", "entryWidth": "42rem", "borderRadius": "3", "baseline": "0.28333rem", "alignHero": "left", "textHero": "<h1>Discover My Blogging Journey: Adventures, Travels, and Hobbies!</h1>\n<p>Join me as I share captivating stories, travel experiences, and dive into the joys of my favorite hobbies.</p>\n<p><a href=\"#\" class=\"btn\">Read more</a></p>", "heightHero": "50vh", "uploadHero": "media/website/jr-korpa-9XngoIpxcEo-unsplash.jpg", "uploadHeroAlt": "", "uploadHeroCaption": "", "relatedPostsNumber": "3", "alignFeed": "left", "feedFeaturedImage": true, "feedFeaturedImageSize": "8", "feedAvatar": true, "feedAuthor": true, "feedDate": true, "feedDateType": "published", "feedtReadMore": true, "navbarHeight": "6rem", "submenu": "auto", "submenuWidth": "240", "mobilemenu": "sidebar", "mobilemenuExpandableSubmenus": true, "colorScheme": "auto", "primaryColor": "#D73A42", "primaryDarkColor": "#FFC074", "fontBody": "system-ui", "disableFontBodyItalic": false, "fontHeadings": "system-ui", "disableFontHeadingsItalic": false, "fontMenu": "var(--body-font)", "fontLogo": "var(--body-font)", "minFontSize": "1.1", "maxFontSize": "1.2", "lineHeight": "1.7", "letterSpacing": "0", "fontBodyWeight": "400", "fontBoldWeight": "600", "fontHeadignsWeight": "500", "fontHeadingsStyle": "normal", "fontHeadingsLineHeight": "1.2", "fontHeadingsletterSpacing": "0", "fontHeadingsTransform": "none", "shareFacebook": true, "shareTwitter": true, "shareTwitterName": "", "sharePinterest": false, "shareMix": false, "shareLinkedin": true, "shareBuffer": false, "shareWhatsApp": true, "socialButtons": true, "socialFacebook": "", "socialTwitter": "", "socialInstagram": "", "socialLinkedin": "", "socialVimeo": "", "socialPinterest": "", "socialYoutube": "", "copyrightText": "<p>© 2025 Melodyze.ai. All Rights Reserved. | Privacy Policy | Terms of Service | Contact Us<br>Follow us: Facebook | Twitter | Instagram | LinkedIn<br><br>Discover the future of music with Melodyze.ai</p>", "searchFeature": false, "createSearchPage": false, "galleryItemGap": "calc(var(--baseline) * 1.5)", "backToTopButton": true, "formatDate": "MMMM D, YYYY", "formatDateCustom": "HH:mm:ss YY/MM/DD", "lazyLoadEffect": "fadein", "faviconUpload": "", "faviconExtension": "image/x-icon", "fontBodyItalic": false, "fontHeadingsItalic": false}, "postConfig": {"displayDate": 1, "displayAuthor": 1, "displayLastUpdatedDate": 1, "displayTags": 1, "displayShareButtons": 1, "displayAuthorBio": 1, "displayPostNavigation": 1, "displayRelatedPosts": 1, "displayComments": 0}, "pageConfig": {"displayDate": 0, "displayAuthor": 0, "displayLastUpdatedDate": 0, "displayShareButtons": 0, "displayAuthorBio": 0, "displayChildPages": 0, "displayComments": 0}, "tagConfig": {"displayFeaturedImage": 1, "displayPostCounter": 1, "displayDescription": 1, "displayPostList": 1}, "authorConfig": {"displayFeaturedImage": 1, "displayAvatar": 1, "displayPostCounter": 1, "displayDescription": 1, "displayWebsite": 1, "displayPostList": 1}, "defaultTemplates": {"post": "", "page": ""}}